version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: crm_postgres
    environment:
      POSTGRES_DB: crm_database
      POSTGRES_USER: crm_user
      POSTGRES_PASSWORD: crm_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - crm_network

  redis:
    image: redis:7-alpine
    container_name: crm_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - crm_network

  pgadmin:
    image: dpage/pgadmin4
    container_name: crm_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - crm_network

volumes:
  postgres_data:
  redis_data:

networks:
  crm_network:
    driver: bridge
