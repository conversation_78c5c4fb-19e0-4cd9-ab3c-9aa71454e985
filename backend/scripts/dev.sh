#!/bin/bash

# Development startup script
echo "🚀 Starting Real Estate CRM Development Environment..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please run setup.sh first."
    exit 1
fi

# Check if node_modules exists
if [ ! -d node_modules ]; then
    echo "❌ Dependencies not installed. Please run setup.sh first."
    exit 1
fi

# Start database services if not running
echo "🐳 Ensuring database services are running..."
docker-compose up -d postgres redis

# Wait a moment for services to start
sleep 3

# Check database connection
echo "🔍 Checking database connection..."
npm run db:generate > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ Database connection failed. Please check your database services."
    exit 1
fi

echo "✅ Database connection successful"

# Start all services
echo "🎯 Starting all microservices..."
npm run dev
