# Database Configuration
DATABASE_URL="postgresql://crm_user:crm_password@localhost:5432/crm_database"
DB_HOST=localhost
DB_PORT=5432
DB_NAME=crm_database
DB_USER=crm_user
DB_PASSWORD=crm_password

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your_refresh_token_secret_here
REFRESH_TOKEN_EXPIRES_IN=30d

# API Gateway Configuration
API_GATEWAY_PORT=3000
API_GATEWAY_HOST=localhost

# Service Ports
AUTH_SERVICE_PORT=3001
LEAD_SERVICE_PORT=3002
CONTACT_SERVICE_PORT=3003
PROPERTY_SERVICE_PORT=3004
DEAL_SERVICE_PORT=3005
NOTIFICATION_SERVICE_PORT=3006

# Environment
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
