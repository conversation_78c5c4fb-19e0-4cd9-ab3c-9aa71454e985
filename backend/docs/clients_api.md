# Clients API Documentation

## Overview
The Clients API provides comprehensive client management functionality for the CRM system. This API allows you to create, read, update, delete, and search clients within your organization.

## Base URL
```
/api/clients
```

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Permission Requirements
- **Read operations**: All authenticated users can view clients
- **Write operations**: Require `AGENT` role or higher (CREATE, UPDATE, DELETE)

## Endpoints

### 1. Get All Clients
**GET** `/api/clients`

Retrieve a paginated list of clients for your organization.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | number | No | Page number (default: 1) |
| `limit` | number | No | Items per page (default: 20, max: 100) |
| `search` | string | No | Search in firstName, lastName, email, phone |
| `status` | ClientStatus | No | Filter by client status |
| `type` | ClientType | No | Filter by client type |
| `source` | LeadSource | No | Filter by lead source |
| `tags` | string | No | Comma-separated tags |
| `sortBy` | string | No | Sort field (default: createdAt) |
| `sortOrder` | 'asc' \| 'desc' | No | Sort order (default: desc) |

#### Response
```json
{
  "success": true,
  "data": {
    "clients": [
      {
        "id": "cuid",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "alternatePhone": "+1234567891",
        "type": "LEAD",
        "status": "ACTIVE",
        "source": "WEBSITE",
        "address": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "country": "US",
        "budget": 500000,
        "preferredAreas": ["Manhattan", "Brooklyn"],
        "propertyTypes": ["SINGLE_FAMILY", "CONDO"],
        "notes": "Looking for a family home",
        "tags": ["first-time-buyer", "urgent"],
        "consentGiven": true,
        "consentDate": "2024-01-15T10:30:00Z",
        "marketingOptIn": true,
        "organizationId": "org-cuid",
        "ownerId": "user-cuid",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. Get Client by ID
**GET** `/api/clients/:id`

Retrieve a specific client by ID.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Client ID |

#### Response
```json
{
  "success": true,
  "data": {
    "id": "cuid",
    "firstName": "John",
    "lastName": "Doe",
    // ... full client object
  }
}
```

### 3. Create Client
**POST** `/api/clients`

Create a new client. Requires `AGENT` role or higher.

#### Request Body
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "alternatePhone": "+1234567891",
  "type": "LEAD",
  "status": "ACTIVE",
  "source": "WEBSITE",
  "address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "zipCode": "10001",
  "country": "US",
  "budget": 500000,
  "preferredAreas": ["Manhattan", "Brooklyn"],
  "propertyTypes": ["SINGLE_FAMILY", "CONDO"],
  "notes": "Looking for a family home",
  "tags": ["first-time-buyer", "urgent"],
  "consentGiven": true,
  "consentDate": "2024-01-15T10:30:00Z",
  "marketingOptIn": true
}
```

#### Required Fields
- `firstName` (string)
- `lastName` (string)

#### Response
```json
{
  "success": true,
  "data": {
    "id": "newly-created-cuid",
    "firstName": "John",
    "lastName": "Doe",
    // ... full client object
  },
  "message": "Client created successfully"
}
```

### 4. Update Client
**PUT** `/api/clients/:id`

Update an existing client. Requires `AGENT` role or higher.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Client ID |

#### Request Body
Any subset of the client fields (partial update):
```json
{
  "firstName": "Jane",
  "phone": "+1987654321",
  "status": "CONVERTED",
  "notes": "Updated notes"
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "id": "cuid",
    "firstName": "Jane",
    // ... updated client object
  },
  "message": "Client updated successfully"
}
```

### 5. Delete Client
**DELETE** `/api/clients/:id`

Soft delete a client. Requires `AGENT` role or higher.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Client ID |

#### Response
```json
{
  "success": true,
  "data": null,
  "message": "Client deleted successfully"
}
```

### 6. Search Clients
**GET** `/api/clients/search`

Advanced search for clients. Requires `AGENT` role or higher.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | Yes | Search query |
| `page` | number | No | Page number (default: 1) |
| `limit` | number | No | Items per page (default: 20, max: 100) |
| `status` | ClientStatus | No | Filter by status |
| `type` | ClientType | No | Filter by type |

#### Response
Same format as "Get All Clients" endpoint.

## Data Types and Enums

### ClientType
```typescript
enum ClientType {
  LEAD = "LEAD",
  PROSPECT = "PROSPECT",
  BUYER = "BUYER",
  SELLER = "SELLER",
  TENANT = "TENANT",
  LANDLORD = "LANDLORD",
  INVESTOR = "INVESTOR"
}
```

### ClientStatus
```typescript
enum ClientStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  CONVERTED = "CONVERTED",
  LOST = "LOST",
  NURTURING = "NURTURING"
}
```

### LeadSource
```typescript
enum LeadSource {
  WEBSITE = "WEBSITE",
  REFERRAL = "REFERRAL",
  SOCIAL_MEDIA = "SOCIAL_MEDIA",
  ADVERTISING = "ADVERTISING",
  COLD_CALL = "COLD_CALL",
  EMAIL_CAMPAIGN = "EMAIL_CAMPAIGN",
  WALK_IN = "WALK_IN",
  SIGN_CALL = "SIGN_CALL",
  OPEN_HOUSE = "OPEN_HOUSE",
  OTHER = "OTHER"
}
```

### PropertyType
```typescript
enum PropertyType {
  SINGLE_FAMILY = "SINGLE_FAMILY",
  CONDO = "CONDO",
  TOWNHOUSE = "TOWNHOUSE",
  MULTI_FAMILY = "MULTI_FAMILY",
  LAND = "LAND",
  COMMERCIAL = "COMMERCIAL",
  INDUSTRIAL = "INDUSTRIAL",
  RENTAL = "RENTAL",
  OTHER = "OTHER"
}
```

### Client Object Structure
```typescript
interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  alternatePhone?: string;
  type: ClientType;
  status: ClientStatus;
  source?: LeadSource;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string; // 2-letter country code
  budget?: number;
  preferredAreas?: string[];
  propertyTypes?: PropertyType[];
  notes?: string;
  tags?: string[];
  consentGiven?: boolean;
  consentDate?: string; // ISO 8601 date
  marketingOptIn?: boolean;
  organizationId: string;
  ownerId: string;
  createdAt: string; // ISO 8601 date
  updatedAt: string; // ISO 8601 date
  isDeleted: boolean;
}
```

## Error Handling

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "statusCode": 400
}
```

### Common Error Codes
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (client doesn't exist)
- `409` - Conflict (duplicate email)
- `500` - Internal Server Error

### Common Error Messages
- "Authentication required"
- "Access denied to this client"
- "Client not found"
- "Client with this email already exists"
- "Search query is required"
- "Agent role required"

## Frontend Usage Examples

### React Hook for Clients API
```typescript
import { useState, useEffect } from 'react';

interface UseClientsProps {
  page?: number;
  limit?: number;
  search?: string;
  status?: ClientStatus;
  type?: ClientType;
}

export const useClients = (params: UseClientsProps = {}) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [pagination, setPagination] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchClients();
  }, [params]);

  const fetchClients = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/clients?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch clients');
      }

      const data = await response.json();
      setClients(data.data.clients);
      setPagination(data.data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return { clients, pagination, loading, error, refetch: fetchClients };
};
```

### Creating a Client
```typescript
const createClient = async (clientData: CreateClientInput) => {
  try {
    const response = await fetch('/api/clients', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(clientData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create client');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error creating client:', error);
    throw error;
  }
};
```

### Updating a Client
```typescript
const updateClient = async (clientId: string, updates: Partial<Client>) => {
  try {
    const response = await fetch(`/api/clients/${clientId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update client');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error updating client:', error);
    throw error;
  }
};
```

### Searching Clients
```typescript
const searchClients = async (query: string, filters: SearchFilters = {}) => {
  try {
    const queryParams = new URLSearchParams({
      query,
      ...filters
    });

    const response = await fetch(`/api/clients/search?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to search clients');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error searching clients:', error);
    throw error;
  }
};
```

## Best Practices

### 1. Authentication
- Always include the JWT token in requests
- Handle token expiration gracefully
- Implement automatic token refresh

### 2. Error Handling
- Check response status codes
- Display user-friendly error messages
- Log detailed errors for debugging

### 3. Data Validation
- Validate data on the frontend before sending
- Use the same validation rules as the backend
- Provide real-time validation feedback

### 4. Performance
- Implement pagination for large datasets
- Use debouncing for search inputs
- Cache frequently accessed data

### 5. User Experience
- Show loading states during API calls
- Provide success/error feedback
- Implement optimistic updates where appropriate

### 6. Security
- Never expose sensitive data in URLs
- Validate permissions before showing UI elements
- Sanitize user inputs

## Rate Limiting
The API implements rate limiting to prevent abuse. If you exceed the rate limit, you'll receive a `429 Too Many Requests` response.

## Data Privacy
- All client data is scoped to the user's organization
- Personal data should be handled according to privacy regulations
- Implement proper consent tracking for marketing communications

## Support
For questions or issues with the Clients API, please contact the backend development team or refer to the main API documentation. 