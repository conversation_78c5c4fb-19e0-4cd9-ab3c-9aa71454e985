###
# Real Estate CRM - Quick Reference Guide
# 
# This file contains the most commonly used API requests for quick testing.
# For complete documentation, see auth.http and organizations.http
###

# Environment Variables
@baseUrl = http://localhost:3000
@contentType = application/json

# Test Credentials
@superAdminEmail = <EMAIL>
@adminEmail = <EMAIL>
@agentEmail = <EMAIL>
@password = admin123

# Tokens (update these after login)
@superAdminToken = 
@adminToken = 
@agentToken = 

###
# QUICK START WORKFLOW
###

### 1. Login as Super Admin
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{superAdminEmail}}",
  "password": "{{password}}"
}

### 2. Get Current User Info
GET {{baseUrl}}/api/auth/me
Authorization: Bearer {{superAdminToken}}

### 3. Get Organizations
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}

### 4. Create New Organization (Super Admin only)
POST {{baseUrl}}/api/organizations
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "name": "Quick Test Organization",
  "slug": "quick-test-org",
  "email": "<EMAIL>"
}

### 5. Register New User (Admin+ only)
POST {{baseUrl}}/api/auth/register
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "New",
  "lastName": "User",
  "organizationId": "org-id-from-step-4",
  "role": "AGENT"
}

### 6. Logout
POST {{baseUrl}}/api/auth/logout
Authorization: Bearer {{superAdminToken}}

###
# COMMON AUTHENTICATION OPERATIONS
###

### Login as Admin
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{adminEmail}}",
  "password": "{{password}}"
}

### Login as Agent
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{agentEmail}}",
  "password": "{{password}}"
}

### Refresh Access Token
POST {{baseUrl}}/api/auth/refresh
Content-Type: {{contentType}}

{
  "refreshToken": "paste_refresh_token_here"
}

### Change Password
PUT {{baseUrl}}/api/auth/change-password
Authorization: Bearer {{adminToken}}
Content-Type: {{contentType}}

{
  "currentPassword": "{{password}}",
  "newPassword": "newSecurePassword123"
}

###
# COMMON ORGANIZATION OPERATIONS
###

### Get My Organizations
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

### Get Specific Organization
GET {{baseUrl}}/api/organizations/org-id-here
Authorization: Bearer {{adminToken}}

### Update Organization (Admin+ only)
PUT {{baseUrl}}/api/organizations/org-id-here
Authorization: Bearer {{adminToken}}
Content-Type: {{contentType}}

{
  "name": "Updated Organization Name",
  "address": "New Address",
  "phone": "******-NEW-PHONE",
  "email": "<EMAIL>"
}

###
# ERROR TESTING
###

### Test Invalid Login
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

### Test Unauthorized Access
GET {{baseUrl}}/api/organizations

### Test Insufficient Permissions (Agent trying to create org)
POST {{baseUrl}}/api/organizations
Authorization: Bearer {{agentToken}}
Content-Type: {{contentType}}

{
  "name": "Unauthorized Organization",
  "slug": "unauthorized-org"
}

### Test Validation Error
POST {{baseUrl}}/api/auth/register
Authorization: Bearer {{superAdminToken}}
Content-Type: {{contentType}}

{
  "email": "invalid-email",
  "password": "123",
  "firstName": "",
  "lastName": "",
  "organizationId": "",
  "role": "INVALID_ROLE"
}

###
# HEALTH CHECKS
###

### API Gateway Health
GET {{baseUrl}}/health

### Auth Service Health (direct)
GET http://localhost:3001/health

### Organizations Service Health (direct)
GET http://localhost:3003/health

### API Documentation
GET {{baseUrl}}/api

###
# ROLE TESTING SCENARIOS
###

### Super Admin Capabilities
# - Can create organizations ✓
# - Can register users ✓
# - Can access all organizations ✓
# - Can update/delete any organization ✓

### Admin Capabilities  
# - Cannot create organizations ✗
# - Can register users in their org ✓
# - Can only access their organization ✓
# - Can update/delete their organization ✓

### Agent Capabilities
# - Cannot create organizations ✗
# - Cannot register users ✗
# - Can only view their organization ✓
# - Cannot update/delete organization ✗

###
# MULTI-TENANT TESTING
###

### Test Cross-Organization Access (should fail)
# 1. Login as user from Organization A
# 2. Try to access Organization B's data
# 3. Should receive 403 Forbidden

### Test Organization Isolation
# 1. Login as users from different organizations
# 2. Each should only see their own organization's data
# 3. No cross-organization data leakage

###
# PERFORMANCE TESTING
###

### Rate Limiting Test (send multiple requests quickly)
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/api/organizations
Authorization: Bearer {{adminToken}}

###
# TOKEN LIFECYCLE TESTING
###

### 1. Login to get fresh tokens
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "{{adminEmail}}",
  "password": "{{password}}"
}

### 2. Use access token immediately
GET {{baseUrl}}/api/auth/me
Authorization: Bearer paste_fresh_access_token_here

### 3. Wait for token to expire (15 minutes) then test
GET {{baseUrl}}/api/auth/me
Authorization: Bearer expired_access_token_here

### 4. Use refresh token to get new access token
POST {{baseUrl}}/api/auth/refresh
Content-Type: {{contentType}}

{
  "refreshToken": "paste_refresh_token_here"
}

### 5. Use new access token
GET {{baseUrl}}/api/auth/me
Authorization: Bearer new_access_token_here

### 6. Logout to invalidate tokens
POST {{baseUrl}}/api/auth/logout
Authorization: Bearer current_access_token_here

### 7. Try to use invalidated token (should fail)
GET {{baseUrl}}/api/auth/me
Authorization: Bearer invalidated_access_token_here

###
# NOTES FOR FRONTEND DEVELOPERS
###

# Base URLs:
# - Development: http://localhost:3000
# - Staging: https://api-staging.yourcrm.com  
# - Production: https://api.yourcrm.com

# Authentication:
# - Include "Authorization: Bearer {token}" header
# - Access tokens expire in 15 minutes
# - Refresh tokens expire in 7 days
# - Handle 401 responses by refreshing tokens

# Error Handling:
# - All responses include "success" boolean
# - Error responses include "error" code and "message"
# - Validation errors include "details" array
# - Always check "success" field first

# Organization Context:
# - Users automatically see only their organization's data
# - Organization ID is embedded in JWT token
# - No need to pass organization ID in requests
# - Cross-organization access is prevented

###
