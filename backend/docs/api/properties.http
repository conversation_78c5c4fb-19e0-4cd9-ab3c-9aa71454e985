### Properties Service API Tests
### Base URL: http://localhost:3004
### Authentication: JWT Bearer Token required for all endpoints

@baseUrl = http://localhost:3000
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************.C7BH7AFzSemjhgPpuWkDKk11zoRfhKuC3QwXfVLgi-o

### Variables for testing
@propertyId = clp123abc456
@assigneeId = user-456

###############################################################################
### 1. Health Check
###############################################################################

### Health Check
GET {{baseUrl}}/health

###############################################################################
### 2. List Properties with Filtering
###############################################################################

### Get all properties (basic)
GET {{baseUrl}}/properties
Authorization: Bearer {{token}}

### Get properties with pagination
GET {{baseUrl}}/properties?page=1&limit=10
Authorization: Bearer {{token}}

### Search properties by text
GET {{baseUrl}}/properties?search=downtown condo
Authorization: Bearer {{token}}

### Filter by location
GET {{baseUrl}}/properties?city=Springfield&state=IL&zipCode=62701
Authorization: Bearer {{token}}

### Filter by property type and status
GET {{baseUrl}}/properties?propertyType=SINGLE_FAMILY&status=AVAILABLE
Authorization: Bearer {{token}}

### Filter by price range
GET {{baseUrl}}/properties?minPrice=200000&maxPrice=500000&priceType=listPrice
Authorization: Bearer {{token}}

### Filter by size (square meters)
GET {{baseUrl}}/properties?minSquareMeters=100&maxSquareMeters=300
Authorization: Bearer {{token}}

### Filter by lot size
GET {{baseUrl}}/properties?minLotSize=500&maxLotSize=1000
Authorization: Bearer {{token}}

### Filter by bedrooms and bathrooms
GET {{baseUrl}}/properties?minBedrooms=2&maxBedrooms=4&minBathrooms=1&maxBathrooms=3
Authorization: Bearer {{token}}

### Filter by date range
GET {{baseUrl}}/properties?listedAfter=2024-01-01&listedBefore=2024-12-31
Authorization: Bearer {{token}}

### Filter by features and amenities
GET {{baseUrl}}/properties?features=garage,garden&amenities=pool,gym
Authorization: Bearer {{token}}

### Filter by status flags
GET {{baseUrl}}/properties?isFeatured=true&isActive=true
Authorization: Bearer {{token}}

### Filter by assignee
GET {{baseUrl}}/properties?assigneeId={{assigneeId}}
Authorization: Bearer {{token}}

### Sort by price (ascending)
GET {{baseUrl}}/properties?sortBy=price&sortOrder=asc
Authorization: Bearer {{token}}

### Sort by size (descending)
GET {{baseUrl}}/properties?sortBy=size&sortOrder=desc
Authorization: Bearer {{token}}

### Sort by listing date (newest first)
GET {{baseUrl}}/properties?sortBy=listingDate&sortOrder=desc
Authorization: Bearer {{token}}

### Complex filtering example
GET {{baseUrl}}/properties?city=Springfield&propertyType=SINGLE_FAMILY&minPrice=300000&maxPrice=400000&minBedrooms=3&isFeatured=true&sortBy=price&sortOrder=asc&page=1&limit=5
Authorization: Bearer {{token}}

###############################################################################
### 3. Get Property by ID
###############################################################################

### Get specific property by ID
GET {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}

### Get non-existent property (should return 404)
GET {{baseUrl}}/properties/non-existent-id
Authorization: Bearer {{token}}

###############################################################################
### 4. Create Property
###############################################################################

### Create a new single family home
POST {{baseUrl}}/api/properties
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Beautiful Family Home",
  "description": "A stunning 3-bedroom family home with modern amenities and a spacious backyard",
  "propertyType": "SINGLE_FAMILY",
  "status": "AVAILABLE",
  "address": "123 Main Street",
  "city": "Springfield",
  "state": "IL",
  "zipCode": "62701",
  "country": "US",
  "latitude": 39.7817,
  "longitude": -89.6501,
  "bedrooms": 3,
  "bathrooms": 2.5,
  "squareMeters": 200,
  "lotSizeMeters": 800,
  "yearBuilt": 2015,
  "listPrice": 350000,
  "pricePerSquareMeter": 1750,
  "features": ["garage", "garden", "fireplace", "hardwood_floors"],
  "amenities": ["pool", "gym", "playground"],
  "images": [
    "https://example.com/property/image1.jpg",
    "https://example.com/property/image2.jpg"
  ],
  "virtualTourUrl": "https://example.com/virtual-tour/123",
  "mlsNumber": "MLS123456",
  "listingDate": "2024-01-01T00:00:00.000Z",
  "expirationDate": "2024-12-31T23:59:59.000Z",
  "daysOnMarket": 30,
  "isFeatured": true
}

### Create a condo
POST {{baseUrl}}/properties
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Modern Downtown Condo",
  "description": "Luxury 2-bedroom condo in the heart of downtown with city views",
  "propertyType": "CONDO",
  "status": "AVAILABLE",
  "address": "456 Oak Avenue, Unit 12B",
  "city": "Springfield",
  "state": "IL",
  "zipCode": "62702",
  "country": "US",
  "bedrooms": 2,
  "bathrooms": 2,
  "squareMeters": 120,
  "yearBuilt": 2020,
  "listPrice": 280000,
  "pricePerSquareMeter": 2333,
  "features": ["balcony", "parking", "elevator"],
  "amenities": ["concierge", "rooftop", "fitness_center"],
  "images": ["https://example.com/condo/image1.jpg"],
  "mlsNumber": "MLS789012",
  "listingDate": "2024-01-15T00:00:00.000Z",
  "isFeatured": false
}

### Create commercial property
POST {{baseUrl}}/properties
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Prime Commercial Space",
  "description": "Excellent location for retail or office space",
  "propertyType": "COMMERCIAL",
  "status": "AVAILABLE",
  "address": "789 Business Boulevard",
  "city": "Springfield",
  "state": "IL",
  "zipCode": "62703",
  "country": "US",
  "squareMeters": 500,
  "yearBuilt": 2010,
  "listPrice": 750000,
  "features": ["parking", "loading_dock"],
  "amenities": ["security", "elevator"],
  "mlsNumber": "MLS345678"
}

### Create property with minimal required fields
POST {{baseUrl}}/properties
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Basic Property Listing",
  "propertyType": "OTHER",
  "address": "999 Simple Street",
  "city": "Test City",
  "state": "TS"
}

### Create property with invalid assignee (should fail)
POST {{baseUrl}}/properties
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Invalid Assignee Property",
  "propertyType": "SINGLE_FAMILY",
  "address": "999 Invalid Street",
  "city": "Invalid City",
  "state": "IV",
  "assigneeId": "invalid-user-id"
}

### Create property with missing required fields (should fail)
POST {{baseUrl}}/properties
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "description": "Missing required fields"
}

###############################################################################
### 5. Update Property
###############################################################################

### Update property basic information
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Updated Property Title",
  "description": "Updated description with new details",
  "listPrice": 375000,
  "status": "UNDER_CONTRACT"
}

### Update property with new assignee
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "assigneeId": "{{assigneeId}}"
}

### Remove assignee from property
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "assigneeId": null
}

### Update property features and amenities
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "features": ["garage", "garden", "pool", "updated_kitchen"],
  "amenities": ["pool", "gym", "tennis_court"],
  "bedrooms": 4,
  "bathrooms": 3,
  "squareMeters": 250
}

### Update property pricing
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "listPrice": 400000,
  "salePrice": 395000,
  "pricePerSquareMeter": 1600
}

### Update property status to sold
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "status": "SOLD",
  "salePrice": 360000
}

### Update property with invalid assignee (should fail)
PUT {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "assigneeId": "invalid-user-from-different-org"
}

### Update non-existent property (should return 404)
PUT {{baseUrl}}/properties/non-existent-id
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "This should fail"
}

###############################################################################
### 6. Advanced Search
###############################################################################

### Search properties by query
GET {{baseUrl}}/properties/search?query=family home
Authorization: Bearer {{token}}

### Search by address
GET {{baseUrl}}/properties/search?query=Main Street
Authorization: Bearer {{token}}

### Search by city
GET {{baseUrl}}/properties/search?query=Springfield
Authorization: Bearer {{token}}

### Search by MLS number
GET {{baseUrl}}/properties/search?query=MLS123456
Authorization: Bearer {{token}}

### Search with additional filters
GET {{baseUrl}}/properties/search?query=condo&propertyType=CONDO&minPrice=200000&maxPrice=300000&city=Springfield
Authorization: Bearer {{token}}

### Search with pagination
GET {{baseUrl}}/properties/search?query=property&page=1&limit=5
Authorization: Bearer {{token}}

### Search without query (should fail)
GET {{baseUrl}}/properties/search
Authorization: Bearer {{token}}

###############################################################################
### 7. Delete Property
###############################################################################

### Delete property (soft delete)
DELETE {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}

### Delete non-existent property (should return 404)
DELETE {{baseUrl}}/properties/non-existent-id
Authorization: Bearer {{token}}

### Try to delete already deleted property (should return 404)
DELETE {{baseUrl}}/properties/{{propertyId}}
Authorization: Bearer {{token}}

###############################################################################
### 8. Error Scenarios
###############################################################################

### Unauthorized request (no token)
GET {{baseUrl}}/properties

### Invalid token
GET {{baseUrl}}/properties
Authorization: Bearer invalid-token

### Invalid property ID format
GET {{baseUrl}}/properties/invalid-id-format
Authorization: Bearer {{token}}

### Invalid query parameters
GET {{baseUrl}}/properties?minPrice=invalid&maxPrice=also-invalid
Authorization: Bearer {{token}}

### Exceed maximum limit
GET {{baseUrl}}/properties?limit=1000
Authorization: Bearer {{token}}

###############################################################################
### 9. Complex Filtering Scenarios
###############################################################################

### Luxury properties filter
GET {{baseUrl}}/properties?minPrice=500000&minBedrooms=4&minBathrooms=3&features=pool&amenities=gym&isFeatured=true&sortBy=price&sortOrder=desc
Authorization: Bearer {{token}}

### Starter homes filter
GET {{baseUrl}}/properties?maxPrice=300000&minBedrooms=2&maxBedrooms=3&propertyType=SINGLE_FAMILY&status=AVAILABLE&sortBy=price&sortOrder=asc
Authorization: Bearer {{token}}

### Investment properties filter
GET {{baseUrl}}/properties?propertyType=MULTI_FAMILY&status=AVAILABLE&minSquareMeters=200&sortBy=pricePerSquareMeter&sortOrder=asc
Authorization: Bearer {{token}}

### Recently listed properties
GET {{baseUrl}}/properties?listedAfter=2024-07-01&sortBy=listingDate&sortOrder=desc&limit=10
Authorization: Bearer {{token}}

### Properties needing attention (no assignee)
GET {{baseUrl}}/properties?assigneeId=&status=AVAILABLE&sortBy=createdAt&sortOrder=asc
Authorization: Bearer {{token}}
