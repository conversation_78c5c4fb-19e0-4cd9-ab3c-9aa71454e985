# Properties Service API Documentation

## Overview

The Properties Service provides comprehensive property management functionality for the Real Estate CRM system. It supports full CRUD operations with advanced filtering, searching, and sorting capabilities.

**Base URL**: `http://localhost:3004`  
**Authentication**: JWT Bearer Token required for all endpoints  
**Content-Type**: `application/json`

## Authentication

All endpoints require authentication via JWT Bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Property Model

### Property Object Structure

```json
{
  "id": "string (cuid)",
  "organizationId": "string",
  "title": "string",
  "description": "string | null",
  "propertyType": "PropertyType enum",
  "status": "PropertyStatus enum",
  
  // Location
  "address": "string",
  "city": "string", 
  "state": "string",
  "zipCode": "string | null",
  "country": "string (default: US)",
  "latitude": "number | null",
  "longitude": "number | null",
  
  // Property Details
  "bedrooms": "number | null",
  "bathrooms": "number | null", 
  "squareMeters": "number | null",
  "lotSizeMeters": "number | null",
  "yearBuilt": "number | null",
  
  // Pricing
  "listPrice": "number | null",
  "salePrice": "number | null",
  "rentPrice": "number | null", 
  "pricePerSquareMeter": "number | null",
  
  // Assignment
  "assigneeId": "string | null",
  "assignee": {
    "id": "string",
    "firstName": "string",
    "lastName": "string", 
    "email": "string"
  } | null,
  
  // Features & Media
  "features": "string[]",
  "amenities": "string[]",
  "images": "string[]",
  "virtualTourUrl": "string | null",
  
  // Listing Details
  "mlsNumber": "string | null",
  "listingDate": "string (ISO date) | null",
  "expirationDate": "string (ISO date) | null", 
  "daysOnMarket": "number | null",
  
  // Status & Timestamps
  "isActive": "boolean",
  "isFeatured": "boolean",
  "isDeleted": "boolean",
  "deletedAt": "string (ISO date) | null",
  "createdAt": "string (ISO date)",
  "updatedAt": "string (ISO date)"
}
```

### Enums

#### PropertyType
- `SINGLE_FAMILY`
- `CONDO` 
- `TOWNHOUSE`
- `MULTI_FAMILY`
- `LAND`
- `COMMERCIAL`
- `INDUSTRIAL`
- `RENTAL`
- `OTHER`

#### PropertyStatus
- `AVAILABLE`
- `UNDER_CONTRACT`
- `SOLD`
- `RENTED`
- `OFF_MARKET`
- `COMING_SOON`
- `WITHDRAWN`

## API Endpoints

### 1. List Properties with Filtering

**GET** `/properties`

Retrieve a paginated list of properties with comprehensive filtering options.

#### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | number | Page number (min: 1) | `1` |
| `limit` | number | Items per page (min: 1, max: 100) | `20` |
| `search` | string | Text search across title, description, address | `"downtown condo"` |

##### Location Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `city` | string | Filter by city | `"Springfield"` |
| `state` | string | Filter by state | `"IL"` |
| `zipCode` | string | Filter by zip code | `"62701"` |

##### Property Filters  
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `propertyType` | PropertyType | Filter by property type | `"SINGLE_FAMILY"` |
| `status` | PropertyStatus | Filter by property status | `"AVAILABLE"` |
| `assigneeId` | string | Filter by assigned agent | `"user-123"` |

##### Size Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `minSquareMeters` | number | Minimum square meters | `100` |
| `maxSquareMeters` | number | Maximum square meters | `500` |
| `minLotSize` | number | Minimum lot size | `200` |
| `maxLotSize` | number | Maximum lot size | `1000` |

##### Bedroom/Bathroom Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `minBedrooms` | number | Minimum bedrooms | `2` |
| `maxBedrooms` | number | Maximum bedrooms | `5` |
| `minBathrooms` | number | Minimum bathrooms | `1` |
| `maxBathrooms` | number | Maximum bathrooms | `3` |

##### Price Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `minPrice` | number | Minimum price | `200000` |
| `maxPrice` | number | Maximum price | `500000` |
| `priceType` | string | Price field to filter (`listPrice`, `salePrice`, `rentPrice`) | `"listPrice"` |

##### Date Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `listedAfter` | string (ISO date) | Properties listed after date | `"2024-01-01"` |
| `listedBefore` | string (ISO date) | Properties listed before date | `"2024-12-31"` |
| `updatedAfter` | string (ISO date) | Properties updated after date | `"2024-06-01"` |
| `updatedBefore` | string (ISO date) | Properties updated before date | `"2024-07-01"` |

##### Feature Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `features` | string or string[] | Filter by features (array or comma-separated) | `"garage,garden"` |
| `amenities` | string or string[] | Filter by amenities | `"pool,gym"` |

##### Status Filters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `isFeatured` | boolean | Filter featured properties | `true` |
| `isActive` | boolean | Filter active properties | `true` |

##### Sorting
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `sortBy` | string | Sort field (`price`, `size`, `bedrooms`, `bathrooms`, `listingDate`, `updatedAt`, `createdAt`) | `"price"` |
| `sortOrder` | string | Sort direction (`asc`, `desc`) | `"desc"` |

#### Response

**Success (200)**
```json
{
  "success": true,
  "data": {
    "properties": [
      {
        "id": "clp123abc456",
        "organizationId": "org-123",
        "title": "Beautiful Family Home",
        "description": "A stunning 3-bedroom family home",
        "propertyType": "SINGLE_FAMILY",
        "status": "AVAILABLE",
        "address": "123 Main Street",
        "city": "Springfield",
        "state": "IL",
        "zipCode": "62701",
        "country": "US",
        "bedrooms": 3,
        "bathrooms": 2.5,
        "squareMeters": 200,
        "listPrice": 350000,
        "assignee": {
          "id": "user-456",
          "firstName": "John",
          "lastName": "Doe",
          "email": "<EMAIL>"
        },
        "features": ["garage", "garden"],
        "amenities": ["pool", "gym"],
        "isFeatured": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "message": "Properties fetched successfully"
}
```

### 2. Get Property by ID

**GET** `/properties/:id`

Retrieve a specific property by its ID.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Property ID (cuid) |

#### Response

**Success (200)**
```json
{
  "success": true,
  "data": {
    "id": "clp123abc456",
    "organizationId": "org-123",
    "title": "Beautiful Family Home",
    "description": "A stunning 3-bedroom family home with modern amenities",
    "propertyType": "SINGLE_FAMILY",
    "status": "AVAILABLE",
    "address": "123 Main Street",
    "city": "Springfield",
    "state": "IL",
    "zipCode": "62701",
    "country": "US",
    "latitude": 39.7817,
    "longitude": -89.6501,
    "bedrooms": 3,
    "bathrooms": 2.5,
    "squareMeters": 200,
    "lotSizeMeters": 800,
    "yearBuilt": 2015,
    "listPrice": 350000,
    "salePrice": null,
    "rentPrice": null,
    "pricePerSquareMeter": 1750,
    "assigneeId": "user-456",
    "assignee": {
      "id": "user-456",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    "features": ["garage", "garden", "fireplace"],
    "amenities": ["pool", "gym"],
    "images": ["https://example.com/image1.jpg"],
    "virtualTourUrl": "https://example.com/tour1",
    "mlsNumber": "MLS123456",
    "listingDate": "2024-01-01T00:00:00.000Z",
    "expirationDate": "2024-12-31T23:59:59.000Z",
    "daysOnMarket": 30,
    "isActive": true,
    "isFeatured": true,
    "isDeleted": false,
    "deletedAt": null,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Property fetched successfully"
}
```

**Error (404)**
```json
{
  "success": false,
  "message": "Property not found"
}
```

### 3. Create Property

**POST** `/properties`

Create a new property. Requires AGENT role or higher.

#### Request Body

```json
{
  "title": "Beautiful Family Home",
  "description": "A stunning 3-bedroom family home with modern amenities",
  "propertyType": "SINGLE_FAMILY",
  "status": "AVAILABLE",
  "address": "123 Main Street",
  "city": "Springfield",
  "state": "IL",
  "zipCode": "62701",
  "country": "US",
  "latitude": 39.7817,
  "longitude": -89.6501,
  "bedrooms": 3,
  "bathrooms": 2.5,
  "squareMeters": 200,
  "lotSizeMeters": 800,
  "yearBuilt": 2015,
  "listPrice": 350000,
  "pricePerSquareMeter": 1750,
  "assigneeId": "user-456",
  "features": ["garage", "garden", "fireplace"],
  "amenities": ["pool", "gym"],
  "images": ["https://example.com/image1.jpg"],
  "virtualTourUrl": "https://example.com/tour1",
  "mlsNumber": "MLS123456",
  "listingDate": "2024-01-01T00:00:00.000Z",
  "expirationDate": "2024-12-31T23:59:59.000Z",
  "daysOnMarket": 30,
  "isFeatured": true
}
```

#### Required Fields
- `title` (string)
- `propertyType` (PropertyType enum)
- `address` (string)
- `city` (string)
- `state` (string)

#### Response

**Success (201)**
```json
{
  "success": true,
  "data": {
    // Complete property object with generated ID and timestamps
  },
  "message": "Property created successfully"
}
```

**Error (400)**
```json
{
  "success": false,
  "message": "Assignee not found or does not belong to your organization"
}
```

### 4. Update Property

**PUT** `/properties/:id`

Update an existing property. Requires AGENT role or higher.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Property ID (cuid) |

#### Request Body

All fields are optional. Only include fields you want to update:

```json
{
  "title": "Updated Property Title",
  "description": "Updated description",
  "status": "UNDER_CONTRACT",
  "listPrice": 375000,
  "salePrice": 370000,
  "bedrooms": 4,
  "bathrooms": 3,
  "assigneeId": "new-user-456",
  "features": ["garage", "garden", "pool", "updated_kitchen"],
  "amenities": ["pool", "gym", "tennis_court"],
  "isFeatured": false
}
```

#### Response

**Success (200)**
```json
{
  "success": true,
  "data": {
    // Complete updated property object
  },
  "message": "Property updated successfully"
}
```

**Error (404)**
```json
{
  "success": false,
  "message": "Property not found"
}
```

### 5. Delete Property

**DELETE** `/properties/:id`

Soft delete a property. Requires AGENT role or higher.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | string | Property ID (cuid) |

#### Response

**Success (200)**
```json
{
  "success": true,
  "message": "Property deleted successfully"
}
```

**Error (404)**
```json
{
  "success": false,
  "message": "Property not found"
}
```

### 6. Advanced Search

**GET** `/properties/search`

Perform advanced text search across properties. Requires AGENT role or higher.

#### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `query` | string | Yes | Search query | `"family home"` |
| `page` | number | No | Page number (min: 1) | `1` |
| `limit` | number | No | Items per page (min: 1, max: 100) | `20` |
| `propertyType` | PropertyType | No | Filter by property type | `"SINGLE_FAMILY"` |
| `status` | PropertyStatus | No | Filter by property status | `"AVAILABLE"` |
| `minPrice` | number | No | Minimum price | `200000` |
| `maxPrice` | number | No | Maximum price | `500000` |
| `city` | string | No | Filter by city | `"Springfield"` |
| `state` | string | No | Filter by state | `"IL"` |

#### Search Fields
The search query will match against:
- Property title
- Property description
- Address
- City
- State
- Zip code
- MLS number
- Features (exact match)
- Amenities (exact match)

#### Response

**Success (200)**
```json
{
  "success": true,
  "data": {
    "properties": [
      // Array of matching property objects
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 25,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "message": "Properties searched successfully"
}
```

**Error (400)**
```json
{
  "success": false,
  "message": "Search query is required"
}
```

## Error Handling

### HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| `200` | Success |
| `201` | Created (for POST requests) |
| `400` | Bad Request (validation errors, invalid parameters) |
| `401` | Unauthorized (missing or invalid token) |
| `403` | Forbidden (insufficient permissions) |
| `404` | Not Found (property not found) |
| `500` | Internal Server Error |

### Error Response Format

All error responses follow this format:

```json
{
  "success": false,
  "message": "Error description"
}
```

### Common Error Messages

- `"Authentication required"` - Missing Authorization header
- `"Property not found"` - Property doesn't exist or doesn't belong to your organization
- `"Assignee not found or does not belong to your organization"` - Invalid assignee ID
- `"Search query is required"` - Missing required query parameter for search
- `"Failed to create property"` - General creation error
- `"Failed to update property"` - General update error
- `"Failed to delete property"` - General deletion error

## Usage Examples

### Example 1: Find Luxury Properties

```http
GET /properties?minPrice=500000&minBedrooms=4&features=pool&amenities=gym&isFeatured=true&sortBy=price&sortOrder=desc
```

### Example 2: Find Starter Homes

```http
GET /properties?maxPrice=300000&propertyType=SINGLE_FAMILY&status=AVAILABLE&minBedrooms=2&maxBedrooms=3&sortBy=price&sortOrder=asc
```

### Example 3: Find Recently Listed Properties

```http
GET /properties?listedAfter=2024-07-01&sortBy=listingDate&sortOrder=desc&limit=10
```

### Example 4: Search for Downtown Condos

```http
GET /properties/search?query=downtown&propertyType=CONDO&minPrice=200000&maxPrice=400000
```

### Example 5: Find Properties by Agent

```http
GET /properties?assigneeId=user-456&status=AVAILABLE&sortBy=createdAt&sortOrder=desc
```

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- Standard rate limit applies to all endpoints
- Higher limits may apply for authenticated users
- Rate limit headers are included in responses

## Pagination

All list endpoints support pagination:
- Default page size: 20 items
- Maximum page size: 100 items
- Pagination metadata included in all responses
- Use `page` and `limit` parameters to control pagination

## Multi-Tenant Isolation

All operations are automatically scoped to your organization:
- Properties are filtered by your organization ID
- Cross-organization access is prevented
- Assignees must belong to your organization

## Testing

Use the provided HTTP test file (`docs/api/properties.http`) for comprehensive API testing with various scenarios including:
- All CRUD operations
- Complex filtering combinations
- Error scenarios
- Edge cases

For more examples, see the HTTP test file in `docs/api/properties.http`.
