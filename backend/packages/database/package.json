{"name": "@crm/database", "version": "1.0.0", "description": "Database package with Prisma ORM for CRM application", "main": "index.ts", "scripts": {"db:generate": "dotenv -e ../../.env -- prisma generate", "db:push": "dotenv -e ../../.env -- prisma db push", "db:migrate": "dotenv -e ../../.env -- prisma migrate dev", "db:studio": "dotenv -e ../../.env -- prisma studio", "db:seed": "dotenv -e ../../.env -- ts-node seed.ts", "db:reset": "dotenv -e ../../.env -- prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.0.0", "prisma": "^5.0.0"}, "devDependencies": {"tsx": "^4.0.0", "@types/node": "^20.0.0", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.0", "dotenv-cli": "^7.3.0"}}