import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting comprehensive database seeding...');

  // Clean existing data (in development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 Cleaning existing data...');
    await prisma.auditLog.deleteMany();
    await prisma.customFieldValue.deleteMany();
    await prisma.customField.deleteMany();
    await prisma.document.deleteMany();
    await prisma.communication.deleteMany();
    await prisma.activity.deleteMany();
    await prisma.deal.deleteMany();
    await prisma.dealStage.deleteMany();
    await prisma.dealPipeline.deleteMany();
    await prisma.client.deleteMany();
    await prisma.property.deleteMany();
    await prisma.permission.deleteMany();
    await prisma.user.deleteMany();
    await prisma.organization.deleteMany();
  }

  // Create Organizations
  console.log('🏢 Creating organizations...');

  const org1 = await prisma.organization.create({
    data: {
      name: 'Premier Real Estate Group',
      slug: 'premier-real-estate',
      domain: 'premier-realestate.com',
      email: '<EMAIL>',
      phone: '******-0100',
      address: '123 Main Street, Downtown, NY 10001',
      website: 'https://premier-realestate.com',
      subscriptionPlan: 'PROFESSIONAL',
      subscriptionStatus: 'ACTIVE',
      maxUsers: 25,
      maxProperties: 1000,
    },
  });

  const org2 = await prisma.organization.create({
    data: {
      name: 'Sunset Properties LLC',
      slug: 'sunset-properties',
      domain: 'sunsetproperties.com',
      email: '<EMAIL>',
      phone: '******-0200',
      address: '456 Oak Avenue, Westside, CA 90210',
      website: 'https://sunsetproperties.com',
      subscriptionPlan: 'STARTER',
      subscriptionStatus: 'TRIAL',
      trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      maxUsers: 5,
      maxProperties: 100,
    },
  });

  // Create Users
  console.log('👥 Creating users...');

  // Premier Real Estate Users
  const adminUser1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      firstName: 'Michael',
      lastName: 'Thompson',
      title: 'Managing Director',
      phone: '******-0101',
      role: 'ADMIN',
      organizationId: org1.id,
      isVerified: true,
    },
  });

  const manager1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('manager123', 10),
      firstName: 'Sarah',
      lastName: 'Williams',
      title: 'Sales Manager',
      phone: '******-0102',
      role: 'MANAGER',
      organizationId: org1.id,
      isVerified: true,
    },
  });

  const agent1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: 'John',
      lastName: 'Smith',
      title: 'Senior Real Estate Agent',
      phone: '******-0103',
      role: 'AGENT',
      organizationId: org1.id,
      isVerified: true,
    },
  });

  const agent2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: 'Emily',
      lastName: 'Davis',
      title: 'Real Estate Agent',
      phone: '******-0104',
      role: 'AGENT',
      organizationId: org1.id,
      isVerified: true,
    },
  });

  // Sunset Properties Users
  const adminUser2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10),
      firstName: 'David',
      lastName: 'Rodriguez',
      title: 'Owner/Broker',
      phone: '******-0201',
      role: 'ADMIN',
      organizationId: org2.id,
      isVerified: true,
    },
  });

  const agent3 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: 'Lisa',
      lastName: 'Chen',
      title: 'Real Estate Agent',
      phone: '******-0202',
      role: 'AGENT',
      organizationId: org2.id,
      isVerified: true,
    },
  });

  // Create Deal Pipelines and Stages
  console.log('🔄 Creating deal pipelines...');

  const salesPipeline1 = await prisma.dealPipeline.create({
    data: {
      name: 'Residential Sales Pipeline',
      description: 'Standard pipeline for residential property sales',
      isDefault: true,
      organizationId: org1.id,
      stages: {
        create: [
          { name: 'Lead', probability: 10, order: 1 },
          { name: 'Qualified', probability: 25, order: 2 },
          { name: 'Showing Scheduled', probability: 40, order: 3 },
          { name: 'Offer Submitted', probability: 60, order: 4 },
          { name: 'Under Contract', probability: 80, order: 5 },
          { name: 'Closed Won', probability: 100, order: 6, isClosedWon: true },
          { name: 'Closed Lost', probability: 0, order: 7, isClosedLost: true },
        ],
      },
    },
  });

  const rentalPipeline1 = await prisma.dealPipeline.create({
    data: {
      name: 'Rental Pipeline',
      description: 'Pipeline for rental properties',
      organizationId: org1.id,
      stages: {
        create: [
          { name: 'Inquiry', probability: 15, order: 1 },
          { name: 'Application', probability: 50, order: 2 },
          { name: 'Background Check', probability: 75, order: 3 },
          { name: 'Lease Signed', probability: 100, order: 4, isClosedWon: true },
          { name: 'Declined', probability: 0, order: 5, isClosedLost: true },
        ],
      },
    },
  });

  // Create Properties
  console.log('🏠 Creating properties...');

  const property1 = await prisma.property.create({
    data: {
      title: 'Luxury Downtown Condo',
      description: 'Beautiful 2-bedroom, 2-bathroom condo in the heart of downtown with stunning city views.',
      propertyType: 'CONDO',
      status: 'AVAILABLE',
      address: '789 City Center Blvd, Unit 1205',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      bedrooms: 2,
      bathrooms: 2.0,
      squareFeet: 1200,
      listPrice: 850000,
      features: ['City Views', 'Hardwood Floors', 'Modern Kitchen', 'Balcony'],
      amenities: ['Gym', 'Rooftop Deck', 'Concierge', 'Parking'],
      images: [
        'https://example.com/property1/image1.jpg',
        'https://example.com/property1/image2.jpg',
      ],
      mlsNumber: 'MLS123456',
      listingDate: new Date('2024-01-15'),
      assigneeId: agent1.id,
      organizationId: org1.id,
    },
  });

  const property2 = await prisma.property.create({
    data: {
      title: 'Family Home in Suburbs',
      description: 'Spacious 4-bedroom family home with large backyard, perfect for growing families.',
      propertyType: 'SINGLE_FAMILY',
      status: 'UNDER_CONTRACT',
      address: '456 Maple Street',
      city: 'Westchester',
      state: 'NY',
      zipCode: '10601',
      bedrooms: 4,
      bathrooms: 3.0,
      squareFeet: 2500,
      lotSize: 0.5,
      yearBuilt: 2010,
      listPrice: 675000,
      features: ['Large Backyard', 'Updated Kitchen', 'Master Suite', 'Garage'],
      amenities: ['Quiet Neighborhood', 'Good Schools', 'Parks Nearby'],
      assigneeId: agent2.id,
      organizationId: org1.id,
    },
  });

  // Create Clients
  console.log('👥 Creating clients...');

  const client1 = await prisma.client.create({
    data: {
      firstName: 'Robert',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '******-1001',
      type: 'BUYER',
      status: 'ACTIVE',
      source: 'WEBSITE',
      address: '123 Oak Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10002',
      budget: 900000,
      preferredAreas: ['Downtown', 'Midtown'],
      propertyTypes: ['CONDO', 'TOWNHOUSE'],
      ownerId: agent1.id,
      organizationId: org1.id,
      consentGiven: true,
      consentDate: new Date(),
      marketingOptIn: true,
    },
  });

  const client2 = await prisma.client.create({
    data: {
      firstName: 'Jennifer',
      lastName: 'Martinez',
      email: '<EMAIL>',
      phone: '******-1002',
      type: 'SELLER',
      status: 'ACTIVE',
      source: 'REFERRAL',
      address: '789 Pine Avenue',
      city: 'Brooklyn',
      state: 'NY',
      zipCode: '11201',
      ownerId: agent2.id,
      organizationId: org1.id,
      consentGiven: true,
      consentDate: new Date(),
    },
  });

  console.log('✅ Database seeding completed successfully!');
  console.log(`🏢 Organization 1: ${org1.name} (${org1.slug})`);
  console.log(`🏢 Organization 2: ${org2.name} (${org2.slug})`);
  console.log(`👤 Admin 1: <EMAIL> / admin123`);
  console.log(`👤 Admin 2: <EMAIL> / admin123`);
  console.log(`👤 Manager: <EMAIL> / manager123`);
  console.log(`👤 Agents: Use agent123 as password for all agent accounts`);
  console.log(`🏠 Created ${2} properties`);
  console.log(`👥 Created ${2} clients`);
  console.log(`🔄 Created ${2} deal pipelines with stages`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });