{"name": "@crm/shared", "version": "1.0.0", "description": "Shared utilities, types, and middleware for CRM application", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "joi": "^17.9.0", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.0", "winston": "^3.10.0", "redis": "^4.6.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "@types/cors": "^2.8.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}}