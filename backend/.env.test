# Test Environment Configuration
NODE_ENV=test

# Test Database Configuration
DATABASE_URL="postgresql://crm_user:crm_password@localhost:5432/crm_database_test"
DB_HOST=localhost
DB_PORT=5432
DB_NAME=crm_database_test
DB_USER=crm_user
DB_PASSWORD=crm_password

# Test Redis Configuration
REDIS_URL="redis://localhost:6379/1"
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration (use different secrets for testing)
JWT_SECRET=test_jwt_secret_key
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_SECRET=test_refresh_token_secret
REFRESH_TOKEN_EXPIRES_IN=7d

# Test Service Ports (different from dev)
API_GATEWAY_PORT=4000
AUTH_SERVICE_PORT=4001
LEAD_SERVICE_PORT=4002
CONTACT_SERVICE_PORT=4003
PROPERTY_SERVICE_PORT=4004
DEAL_SERVICE_PORT=4005
NOTIFICATION_SERVICE_PORT=4006

# Logging (reduced for tests)
LOG_LEVEL=error
