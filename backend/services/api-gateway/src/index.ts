import express from 'express';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import dotenv from 'dotenv';
import path from 'path';
import { createProxyMiddleware } from 'http-proxy-middleware';
import {
  corsMiddleware,
  development<PERSON>ors,
  error<PERSON><PERSON>ler,
  notFoundHandler,
  generalLimiter,
  createServiceLogger,
} from '@crm/shared';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const app = express();
const PORT = process.env.API_GATEWAY_PORT || 3000;
const logger = createServiceLogger('api-gateway');

// Security middleware
app.use(helmet());
app.use(compression());

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Logging
app.use(morgan('combined'));

// Rate limiting
app.use(generalLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'api-gateway',
    version: '1.0.0',
  });
});

// Service proxy configurations
const services = {
  auth: {
    target: `http://localhost:${process.env.AUTH_SERVICE_PORT || 3001}`,
    pathRewrite: { '^/api/auth': '' },
  },
  organizations: {
    target: `http://localhost:${process.env.ORGANIZATIONS_SERVICE_PORT || 3003}`,
    pathRewrite: { '^/api/organizations': '' },
  },
  clients: {
    target: `http://localhost:${process.env.CLIENTS_SERVICE_PORT || 3002}`,
    pathRewrite: { '^/api/clients': '' },
  },
  properties: {
    target: `http://localhost:${process.env.PROPERTIES_SERVICE_PORT || 3004}`,
    pathRewrite: { '^/api/properties': '' },
  },
};

// Create proxy middleware for each service
Object.entries(services).forEach(([serviceName, config]) => {
  app.use(
    `/api/${serviceName}`,
    createProxyMiddleware({
      target: config.target,
      changeOrigin: true,
      pathRewrite: config.pathRewrite,
      onError: (err, req, res) => {
        logger.error(`Proxy error for ${serviceName}:`, err);
        res.status(503).json({
          success: false,
          error: 'SERVICE_UNAVAILABLE',
          message: `${serviceName} service is currently unavailable`,
          timestamp: new Date().toISOString(),
          path: req.originalUrl,
        });
      },
      onProxyReq: (proxyReq, req, res) => {
        logger.info(`Proxying ${req.method} ${req.originalUrl} to ${serviceName}`);
      },
    })
  );
});

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'Real Estate CRM API',
    version: '1.0.0',
    description: 'API Gateway for Real Estate CRM microservices',
    services: {
      auth: '/api/auth - Authentication and authorization',
      organizations: '/api/organizations - Organization management',
    },
    endpoints: {
      health: '/health - Service health check',
      docs: '/api - This documentation',
    },
    note: 'Authentication and Organizations services are now active.',
  });
});

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 API Gateway running on port ${PORT}`);
  logger.info(`📚 API Documentation available at http://localhost:${PORT}/api`);
  logger.info(`🏥 Health check available at http://localhost:${PORT}/health`);
});

export default app;
