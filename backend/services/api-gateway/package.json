{"name": "@crm/api-gateway", "version": "1.0.0", "description": "API Gateway for CRM microservices", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@crm/shared": "*", "@crm/database": "*", "express": "^4.18.0", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "http-proxy-middleware": "^2.0.6", "dotenv": "^16.3.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/compression": "^1.7.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "nodemon": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}}