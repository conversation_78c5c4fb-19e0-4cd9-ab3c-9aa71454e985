// Test data for organizations service tests
export const mockOrganization = {
  id: 'org-123',
  name: 'Test Organization',
  slug: 'test-org',
  domain: 'test.com',
  logo: null,
  address: '123 Test St',
  phone: '******-0123',
  email: '<EMAIL>',
  website: 'https://test.com',
  timezone: 'UTC',
  currency: 'USD',
  subscriptionPlan: 'STARTER',
  subscriptionStatus: 'ACTIVE',
  trialEndsAt: null,
  subscriptionEndsAt: null,
  maxUsers: 5,
  maxProperties: 100,
  settings: null,
  isActive: true,
  isDeleted: false,
  deletedAt: null,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  organizationId: 'org-123',
  role: 'AGENT',
  firstName: 'Test',
  lastName: 'User',
  isActive: true,
};

export const mockSuperAdmin = {
  id: 'super-admin-123',
  email: '<EMAIL>',
  organizationId: 'org-123',
  role: 'SUPER_ADMIN',
  firstName: 'Super',
  lastName: 'Admin',
  isActive: true,
};

export const mockAdmin = {
  id: 'admin-123',
  email: '<EMAIL>',
  organizationId: 'org-123',
  role: 'ADMIN',
  firstName: 'Admin',
  lastName: 'User',
  isActive: true,
};

export const mockAgent = {
  id: 'agent-123',
  email: '<EMAIL>',
  organizationId: 'org-123',
  role: 'AGENT',
  firstName: 'Agent',
  lastName: 'User',
  isActive: true,
};

export const mockUserFromDifferentOrg = {
  id: 'user-different-org-123',
  email: '<EMAIL>',
  organizationId: 'different-org-123',
  role: 'ADMIN',
  firstName: 'Different',
  lastName: 'User',
  isActive: true,
};

export const mockDifferentOrganization = {
  id: 'different-org-123',
  name: 'Different Organization',
  slug: 'different-org',
  domain: 'different.com',
  logo: null,
  address: '456 Different St',
  phone: '******-9999',
  email: '<EMAIL>',
  website: 'https://different.com',
  timezone: 'UTC',
  currency: 'USD',
  subscriptionPlan: 'STARTER',
  subscriptionStatus: 'ACTIVE',
  trialEndsAt: null,
  subscriptionEndsAt: null,
  maxUsers: 5,
  maxProperties: 100,
  settings: null,
  isActive: true,
  isDeleted: false,
  deletedAt: null,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const mockCreateOrganizationRequest = {
  name: 'New Organization',
  slug: 'new-org',
  domain: 'new.com',
  email: '<EMAIL>',
  phone: '******-1111',
  address: '789 New St',
  website: 'https://new.com',
  timezone: 'UTC',
  currency: 'USD',
};

export const mockUpdateOrganizationRequest = {
  name: 'Updated Organization',
  phone: '******-2222',
  website: 'https://updated.com',
};

export const mockInactiveOrganization = {
  ...mockOrganization,
  id: 'inactive-org-123',
  name: 'Inactive Organization',
  slug: 'inactive-org',
  isActive: false,
};

export const mockDeletedOrganization = {
  ...mockOrganization,
  id: 'deleted-org-123',
  name: 'Deleted Organization',
  slug: 'deleted-org',
  isDeleted: true,
  deletedAt: new Date('2023-06-01'),
};
