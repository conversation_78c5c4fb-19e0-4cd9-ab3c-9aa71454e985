import { PrismaClient } from '@crm/database';
import { PasswordHelper } from '@crm/shared';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://crm_user:crm_password@localhost:5432/crm_database';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

export const testDb = prisma;

// Test data setup
export const setupTestData = async () => {
  // Clean up existing test data
  await cleanupTestData();

  // Create test organizations
  const testOrganization = await testDb.organization.create({
    data: {
      name: 'Test Organization',
      slug: 'test-org',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Test St',
      website: 'https://test.com',
      timezone: 'UTC',
      currency: 'USD',
    },
  });

  const anotherOrganization = await testDb.organization.create({
    data: {
      name: 'Another Organization',
      slug: 'another-org',
      email: '<EMAIL>',
      phone: '******-9999',
      timezone: 'UTC',
      currency: 'USD',
    },
  });

  // Create test users
  const hashedPassword = await PasswordHelper.hash('password123');

  const superAdmin = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      organizationId: testOrganization.id,
      role: 'SUPER_ADMIN',
      isVerified: true,
    },
  });

  const admin = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'ADMIN',
      isVerified: true,
    },
  });

  const agent = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Agent',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  const userFromAnotherOrg = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Another',
      lastName: 'User',
      organizationId: anotherOrganization.id,
      role: 'ADMIN',
      isVerified: true,
    },
  });

  return {
    testOrganization,
    anotherOrganization,
    superAdmin,
    admin,
    agent,
    userFromAnotherOrg,
  };
};

export const cleanupTestData = async () => {
  // Delete in correct order due to foreign key constraints
  await testDb.user.deleteMany({
    where: {
      email: {
        in: [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ],
      },
    },
  });

  await testDb.organization.deleteMany({
    where: {
      slug: {
        in: ['test-org', 'another-org', 'new-test-org', 'updated-test-org'],
      },
    },
  });
};

// Setup and teardown hooks
beforeAll(async () => {
  await setupTestData();
});

afterAll(async () => {
  await cleanupTestData();
  await testDb.$disconnect();
});

export default testDb;
