import { prismaMock } from '../mocks/prisma';
import * as organizationService from '../../services/organizationService';
import {
  mockOrganization,
  mockUser,
  mockSuperAdmin,
  mockAdmin,
  mockAgent,
  mockUserFromDifferentOrg,
  mockDifferentOrganization,
  mockCreateOrganizationRequest,
  mockUpdateOrganizationRequest,
  mockInactiveOrganization,
} from '../mocks/testData';

// Mock logger
jest.mock('@crm/shared', () => ({
  ...jest.requireActual('@crm/shared'),
  createServiceLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

describe('OrganizationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getOrganizations', () => {
    it('should successfully get user organizations', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      prismaMock.organization.findMany.mockResolvedValue([mockOrganization] as any);

      // Act
      const result = await organizationService.getOrganizations(mockUser.id);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual([mockOrganization]);
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        select: { organizationId: true },
      });
      expect(prismaMock.organization.findMany).toHaveBeenCalledWith({
        where: {
          id: mockUser.organizationId,
          isActive: true,
          isDeleted: false,
        },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should fail when user not found', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await organizationService.getOrganizations('nonexistent-user');

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('User not found');
      expect(result.statusCode).toBe(404);
    });

    it('should handle database errors', async () => {
      // Arrange
      prismaMock.user.findUnique.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await organizationService.getOrganizations(mockUser.id);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to fetch organizations');
      expect(result.statusCode).toBe(500);
    });
  });

  describe('getOrganizationById', () => {
    it('should successfully get organization by ID', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      prismaMock.organization.findUnique.mockResolvedValue(mockOrganization as any);

      // Act
      const result = await organizationService.getOrganizationById(mockOrganization.id, mockUser.id);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockOrganization);
    });

    it('should fail when user does not belong to organization', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUserFromDifferentOrg as any);

      // Act
      const result = await organizationService.getOrganizationById(mockOrganization.id, mockUserFromDifferentOrg.id);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Access denied to this organization');
      expect(result.statusCode).toBe(403);
    });

    it('should fail when organization not found', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      prismaMock.organization.findUnique.mockResolvedValue(null);

      // Act
      const result = await organizationService.getOrganizationById(mockUser.organizationId, mockUser.id);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Organization not found');
      expect(result.statusCode).toBe(404);
    });
  });

  describe('createOrganization', () => {
    it('should successfully create organization', async () => {
      // Arrange
      prismaMock.organization.findUnique
        .mockResolvedValueOnce(null) // No existing slug
        .mockResolvedValueOnce(null); // No existing domain
      prismaMock.organization.create.mockResolvedValue(mockOrganization as any);

      // Act
      const result = await organizationService.createOrganization(mockCreateOrganizationRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockOrganization);
      expect(prismaMock.organization.create).toHaveBeenCalledWith({
        data: mockCreateOrganizationRequest,
      });
    });

    it('should fail when slug already exists', async () => {
      // Arrange
      prismaMock.organization.findUnique.mockResolvedValue(mockOrganization as any);

      // Act
      const result = await organizationService.createOrganization(mockCreateOrganizationRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Organization slug already exists');
      expect(result.statusCode).toBe(409);
    });

    it('should fail when domain already exists', async () => {
      // Arrange
      prismaMock.organization.findUnique
        .mockResolvedValueOnce(null) // No existing slug
        .mockResolvedValueOnce(mockOrganization as any); // Existing domain

      // Act
      const result = await organizationService.createOrganization(mockCreateOrganizationRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Domain already registered to another organization');
      expect(result.statusCode).toBe(409);
    });

    it('should handle database errors', async () => {
      // Arrange
      prismaMock.organization.findUnique.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await organizationService.createOrganization(mockCreateOrganizationRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to create organization');
      expect(result.statusCode).toBe(500);
    });
  });

  describe('updateOrganization', () => {
    it('should successfully update organization as admin', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockAdmin as any);
      prismaMock.organization.findFirst.mockResolvedValue(null); // No conflicts
      prismaMock.organization.update.mockResolvedValue({
        ...mockOrganization,
        ...mockUpdateOrganizationRequest,
      } as any);

      // Act
      const result = await organizationService.updateOrganization(
        mockOrganization.id,
        mockUpdateOrganizationRequest,
        mockAdmin.id
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        ...mockOrganization,
        ...mockUpdateOrganizationRequest,
      });
    });

    it('should successfully update organization as super admin', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockSuperAdmin as any);
      prismaMock.organization.findFirst.mockResolvedValue(null);
      prismaMock.organization.update.mockResolvedValue({
        ...mockOrganization,
        ...mockUpdateOrganizationRequest,
      } as any);

      // Act
      const result = await organizationService.updateOrganization(
        mockOrganization.id,
        mockUpdateOrganizationRequest,
        mockSuperAdmin.id
      );

      // Assert
      expect(result.success).toBe(true);
    });

    it('should fail when user does not belong to organization', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUserFromDifferentOrg as any);

      // Act
      const result = await organizationService.updateOrganization(
        mockOrganization.id,
        mockUpdateOrganizationRequest,
        mockUserFromDifferentOrg.id
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Access denied to this organization');
      expect(result.statusCode).toBe(403);
    });

    it('should fail when user has insufficient permissions', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockAgent as any);

      // Act
      const result = await organizationService.updateOrganization(
        mockOrganization.id,
        mockUpdateOrganizationRequest,
        mockAgent.id
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Insufficient permissions to update organization');
      expect(result.statusCode).toBe(403);
    });
  });

  describe('deleteOrganization', () => {
    it('should successfully delete organization as admin', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockAdmin as any);
      prismaMock.organization.update.mockResolvedValue(mockOrganization as any);

      // Act
      const result = await organizationService.deleteOrganization(mockOrganization.id, mockAdmin.id);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(null);
      expect(prismaMock.organization.update).toHaveBeenCalledWith({
        where: { id: mockOrganization.id },
        data: {
          isDeleted: true,
          deletedAt: expect.any(Date),
          isActive: false,
        },
      });
    });

    it('should fail when user has insufficient permissions', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockAgent as any);

      // Act
      const result = await organizationService.deleteOrganization(mockOrganization.id, mockAgent.id);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Insufficient permissions to delete organization');
      expect(result.statusCode).toBe(403);
    });

    it('should fail when user does not belong to organization', async () => {
      // Arrange
      prismaMock.user.findUnique.mockResolvedValue(mockUserFromDifferentOrg as any);

      // Act
      const result = await organizationService.deleteOrganization(
        mockOrganization.id,
        mockUserFromDifferentOrg.id
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Access denied to this organization');
      expect(result.statusCode).toBe(403);
    });
  });
});
