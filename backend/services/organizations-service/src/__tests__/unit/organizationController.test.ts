import { Request, Response } from 'express';
import { prismaMock } from '../mocks/prisma';
import { AuthenticatedRequest } from '@crm/shared';
import {
  mockOrganization,
  mockUser,
  mockSuperAdmin,
  mockAdmin,
  mockAgent,
  mockCreateOrganizationRequest,
  mockUpdateOrganizationRequest,
} from '../mocks/testData';

// Mock the organization service
jest.mock('../../services/organizationService');

// Mock ResponseHelper
jest.mock('@crm/shared', () => ({
  ...jest.requireActual('@crm/shared'),
  ResponseHelper: {
    success: jest.fn(),
    error: jest.fn(),
    unauthorized: jest.fn(),
    forbidden: jest.fn(),
    notFound: jest.fn(),
    internalServerError: jest.fn(),
  },
  createServiceLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

// Import after mocking
import * as organizationController from '../../controllers/organizationController';
import * as organizationService from '../../services/organizationService';
import { ResponseHelper } from '@crm/shared';

const mockOrganizationService = organizationService as jest.Mocked<typeof organizationService>;
const mockResponseHelper = ResponseHelper as jest.Mocked<typeof ResponseHelper>;

describe('OrganizationController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockAuthenticatedRequest: Partial<AuthenticatedRequest>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockRequest = {
      body: {},
      params: {},
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockAuthenticatedRequest = {
      ...mockRequest,
      user: mockUser,
    };
  });

  describe('getOrganizations', () => {
    it('should successfully get organizations', async () => {
      // Arrange
      const mockResult = {
        success: true,
        data: [mockOrganization],
      };

      mockOrganizationService.getOrganizations.mockResolvedValue(mockResult);

      // Act
      await organizationController.getOrganizations(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockOrganizationService.getOrganizations).toHaveBeenCalledWith(mockUser.id);
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        [mockOrganization],
        'Organizations fetched successfully'
      );
    });

    it('should handle unauthenticated request', async () => {
      // Arrange
      const unauthenticatedRequest = { ...mockRequest, user: undefined };

      // Act
      await organizationController.getOrganizations(
        unauthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.unauthorized).toHaveBeenCalledWith(
        mockResponse,
        'Authentication required'
      );
    });

    it('should handle service errors', async () => {
      // Arrange
      const mockResult = {
        success: false,
        message: 'Database error',
        statusCode: 500,
      };

      mockOrganizationService.getOrganizations.mockResolvedValue(mockResult);

      // Act
      await organizationController.getOrganizations(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.error).toHaveBeenCalledWith(
        mockResponse,
        'Database error',
        500
      );
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      mockOrganizationService.getOrganizations.mockRejectedValue(new Error('Unexpected error'));

      // Act
      await organizationController.getOrganizations(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.internalServerError).toHaveBeenCalledWith(
        mockResponse,
        'Failed to fetch organizations'
      );
    });
  });

  describe('getOrganizationById', () => {
    it('should successfully get organization by ID', async () => {
      // Arrange
      mockAuthenticatedRequest.params = { id: mockOrganization.id };

      const mockResult = {
        success: true,
        data: mockOrganization,
      };

      mockOrganizationService.getOrganizationById.mockResolvedValue(mockResult);

      // Act
      await organizationController.getOrganizationById(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockOrganizationService.getOrganizationById).toHaveBeenCalledWith(
        mockOrganization.id,
        mockUser.id
      );
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        mockOrganization,
        'Organization fetched successfully'
      );
    });

    it('should handle organization not found', async () => {
      // Arrange
      mockAuthenticatedRequest.params = { id: 'nonexistent-org' };

      const mockResult = {
        success: false,
        message: 'Organization not found',
        statusCode: 404,
      };

      mockOrganizationService.getOrganizationById.mockResolvedValue(mockResult);

      // Act
      await organizationController.getOrganizationById(
        mockAuthenticatedRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.error).toHaveBeenCalledWith(
        mockResponse,
        'Organization not found',
        404
      );
    });
  });

  describe('createOrganization', () => {
    it('should successfully create organization as super admin', async () => {
      // Arrange
      const superAdminRequest = {
        ...mockAuthenticatedRequest,
        user: mockSuperAdmin,
        body: mockCreateOrganizationRequest,
      };

      const mockResult = {
        success: true,
        data: mockOrganization,
      };

      mockOrganizationService.createOrganization.mockResolvedValue(mockResult);

      // Act
      await organizationController.createOrganization(
        superAdminRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockOrganizationService.createOrganization).toHaveBeenCalledWith(
        mockCreateOrganizationRequest
      );
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        mockOrganization,
        'Organization created successfully',
        201
      );
    });

    it('should deny creation for non-super admin', async () => {
      // Arrange
      const adminRequest = {
        ...mockAuthenticatedRequest,
        user: mockAdmin,
        body: mockCreateOrganizationRequest,
      };

      // Act
      await organizationController.createOrganization(
        adminRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.forbidden).toHaveBeenCalledWith(
        mockResponse,
        'Only super admins can create organizations'
      );
    });

    it('should handle creation failure', async () => {
      // Arrange
      const superAdminRequest = {
        ...mockAuthenticatedRequest,
        user: mockSuperAdmin,
        body: mockCreateOrganizationRequest,
      };

      const mockResult = {
        success: false,
        message: 'Organization slug already exists',
        statusCode: 409,
      };

      mockOrganizationService.createOrganization.mockResolvedValue(mockResult);

      // Act
      await organizationController.createOrganization(
        superAdminRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.error).toHaveBeenCalledWith(
        mockResponse,
        'Organization slug already exists',
        409
      );
    });
  });

  describe('updateOrganization', () => {
    it('should successfully update organization', async () => {
      // Arrange
      const adminRequest = {
        ...mockAuthenticatedRequest,
        user: mockAdmin,
        params: { id: mockOrganization.id },
        body: mockUpdateOrganizationRequest,
      };

      const updatedOrganization = {
        ...mockOrganization,
        ...mockUpdateOrganizationRequest,
      };

      const mockResult = {
        success: true,
        data: updatedOrganization,
      };

      mockOrganizationService.updateOrganization.mockResolvedValue(mockResult);

      // Act
      await organizationController.updateOrganization(
        adminRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockOrganizationService.updateOrganization).toHaveBeenCalledWith(
        mockOrganization.id,
        mockUpdateOrganizationRequest,
        mockAdmin.id
      );
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        updatedOrganization,
        'Organization updated successfully'
      );
    });

    it('should handle update failure', async () => {
      // Arrange
      const agentRequest = {
        ...mockAuthenticatedRequest,
        user: mockAgent,
        params: { id: mockOrganization.id },
        body: mockUpdateOrganizationRequest,
      };

      const mockResult = {
        success: false,
        message: 'Insufficient permissions to update organization',
        statusCode: 403,
      };

      mockOrganizationService.updateOrganization.mockResolvedValue(mockResult);

      // Act
      await organizationController.updateOrganization(
        agentRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.error).toHaveBeenCalledWith(
        mockResponse,
        'Insufficient permissions to update organization',
        403
      );
    });
  });

  describe('deleteOrganization', () => {
    it('should successfully delete organization', async () => {
      // Arrange
      const adminRequest = {
        ...mockAuthenticatedRequest,
        user: mockAdmin,
        params: { id: mockOrganization.id },
      };

      const mockResult = {
        success: true,
        data: null,
      };

      mockOrganizationService.deleteOrganization.mockResolvedValue(mockResult);

      // Act
      await organizationController.deleteOrganization(
        adminRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockOrganizationService.deleteOrganization).toHaveBeenCalledWith(
        mockOrganization.id,
        mockAdmin.id
      );
      expect(mockResponseHelper.success).toHaveBeenCalledWith(
        mockResponse,
        null,
        'Organization deleted successfully'
      );
    });

    it('should handle delete failure', async () => {
      // Arrange
      const agentRequest = {
        ...mockAuthenticatedRequest,
        user: mockAgent,
        params: { id: mockOrganization.id },
      };

      const mockResult = {
        success: false,
        message: 'Insufficient permissions to delete organization',
        statusCode: 403,
      };

      mockOrganizationService.deleteOrganization.mockResolvedValue(mockResult);

      // Act
      await organizationController.deleteOrganization(
        agentRequest as AuthenticatedRequest,
        mockResponse as Response
      );

      // Assert
      expect(mockResponseHelper.error).toHaveBeenCalledWith(
        mockResponse,
        'Insufficient permissions to delete organization',
        403
      );
    });
  });
});
