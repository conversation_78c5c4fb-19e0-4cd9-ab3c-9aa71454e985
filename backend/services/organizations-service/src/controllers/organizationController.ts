import { Response } from 'express';
import {
  AuthenticatedRequest,
  ResponseHelper,
  createServiceLogger,
} from '@crm/shared';
import * as organizationService from '../services/organizationService';

const logger = createServiceLogger('organization-controller');

export const getOrganizations = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const result = await organizationService.getOrganizations(req.user.id);

    if (!result.success) {
      ResponseHelper.error(res, result.message || 'Failed to fetch organizations', result.statusCode || 500);
      return;
    }

    ResponseHelper.success(res, result.data, 'Organizations fetched successfully');
  } catch (error) {
    logger.error('Get organizations error:', error);
    ResponseHelper.internalServerError(res, 'Failed to fetch organizations');
  }
};

export const getOrganizationById = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const { id } = req.params;
    const result = await organizationService.getOrganizationById(id, req.user.id);

    if (!result.success) {
      if (result.statusCode === 403) {
        ResponseHelper.forbidden(res, result.message);
      } else if (result.statusCode === 404) {
        ResponseHelper.notFound(res, result.message);
      } else {
        ResponseHelper.internalServerError(res, result.message || 'Failed to fetch organization');
      }
      return;
    }

    ResponseHelper.success(res, result.data, 'Organization fetched successfully');
  } catch (error) {
    logger.error('Get organization by ID error:', error);
    ResponseHelper.internalServerError(res, 'Failed to fetch organization');
  }
};

export const createOrganization = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    // Only SUPER_ADMIN can create organizations
    if (req.user.role !== 'SUPER_ADMIN') {
      ResponseHelper.forbidden(res, 'Only super admins can create organizations');
      return;
    }

    const organizationData = req.body;
    const result = await organizationService.createOrganization(organizationData);

    if (!result.success) {
      if (result.statusCode === 409) {
        ResponseHelper.conflict(res, result.message);
      } else {
        ResponseHelper.internalServerError(res, result.message || 'Failed to create organization');
      }
      return;
    }

    logger.info(`Organization created: ${result.data?.name} by ${req.user.email}`);
    ResponseHelper.success(res, result.data, 'Organization created successfully', 201);
  } catch (error) {
    logger.error('Create organization error:', error);
    ResponseHelper.internalServerError(res, 'Failed to create organization');
  }
};

export const updateOrganization = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const { id } = req.params;
    const organizationData = req.body;
    const result = await organizationService.updateOrganization(id, organizationData, req.user.id);

    if (!result.success) {
      if (result.statusCode === 403) {
        ResponseHelper.forbidden(res, result.message);
      } else if (result.statusCode === 409) {
        ResponseHelper.conflict(res, result.message);
      } else {
        ResponseHelper.internalServerError(res, result.message || 'Failed to update organization');
      }
      return;
    }

    logger.info(`Organization updated: ${id} by ${req.user.email}`);
    ResponseHelper.success(res, result.data, 'Organization updated successfully');
  } catch (error) {
    logger.error('Update organization error:', error);
    ResponseHelper.internalServerError(res, 'Failed to update organization');
  }
};

export const deleteOrganization = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const { id } = req.params;
    const result = await organizationService.deleteOrganization(id, req.user.id);

    if (!result.success) {
      if (result.statusCode === 403) {
        ResponseHelper.forbidden(res, result.message);
      } else {
        ResponseHelper.internalServerError(res, result.message || 'Failed to delete organization');
      }
      return;
    }

    logger.info(`Organization deleted: ${id} by ${req.user.email}`);
    ResponseHelper.success(res, null, 'Organization deleted successfully');
  } catch (error) {
    logger.error('Delete organization error:', error);
    ResponseHelper.internalServerError(res, 'Failed to delete organization');
  }
};
