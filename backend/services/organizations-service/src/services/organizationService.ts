import { prisma } from '@crm/database';
import { ServiceResponse, createServiceLogger } from '@crm/shared';
import { 
  Organization, 
  CreateOrganizationRequest, 
  UpdateOrganizationRequest 
} from '@crm/shared';

const logger = createServiceLogger('organization-service');

export const getOrganizations = async (
  userId: string
): Promise<ServiceResponse<Organization[]>> => {
  try {
    // Get user's organization
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
        statusCode: 404,
      };
    }

    const organizations = await prisma.organization.findMany({
      where: {
        id: user.organizationId,
        isActive: true,
        isDeleted: false,
      },
      orderBy: { createdAt: 'desc' },
    });

    return {
      success: true,
      data: organizations,
    };
  } catch (error) {
    logger.error('Get organizations error:', error);
    return {
      success: false,
      message: 'Failed to fetch organizations',
      statusCode: 500,
    };
  }
};

export const getOrganizationById = async (
  organizationId: string,
  userId: string
): Promise<ServiceResponse<Organization>> => {
  try {
    // Verify user belongs to this organization
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user || user.organizationId !== organizationId) {
      return {
        success: false,
        message: 'Access denied to this organization',
        statusCode: 403,
      };
    }

    const organization = await prisma.organization.findUnique({
      where: {
        id: organizationId,
        isActive: true,
        isDeleted: false,
      },
    });

    if (!organization) {
      return {
        success: false,
        message: 'Organization not found',
        statusCode: 404,
      };
    }

    return {
      success: true,
      data: organization,
    };
  } catch (error) {
    logger.error('Get organization by ID error:', error);
    return {
      success: false,
      message: 'Failed to fetch organization',
      statusCode: 500,
    };
  }
};

export const createOrganization = async (
  organizationData: CreateOrganizationRequest
): Promise<ServiceResponse<Organization>> => {
  try {
    // Check if slug is already taken
    const existingOrg = await prisma.organization.findUnique({
      where: { slug: organizationData.slug },
    });

    if (existingOrg) {
      return {
        success: false,
        message: 'Organization slug already exists',
        statusCode: 409,
      };
    }

    // Check if domain is already taken (if provided)
    if (organizationData.domain) {
      const existingDomain = await prisma.organization.findUnique({
        where: { domain: organizationData.domain },
      });

      if (existingDomain) {
        return {
          success: false,
          message: 'Domain already registered to another organization',
          statusCode: 409,
        };
      }
    }

    const newOrganization = await prisma.organization.create({
      data: organizationData,
    });

    return {
      success: true,
      data: newOrganization,
    };
  } catch (error) {
    logger.error('Create organization error:', error);
    return {
      success: false,
      message: 'Failed to create organization',
      statusCode: 500,
    };
  }
};

export const updateOrganization = async (
  organizationId: string,
  organizationData: UpdateOrganizationRequest,
  userId: string
): Promise<ServiceResponse<Organization>> => {
  try {
    // Verify user belongs to this organization and has admin role
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user || user.organizationId !== organizationId) {
      return {
        success: false,
        message: 'Access denied to this organization',
        statusCode: 403,
      };
    }

    if (user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN') {
      return {
        success: false,
        message: 'Insufficient permissions to update organization',
        statusCode: 403,
      };
    }

    // Check if new slug is already taken (if provided)
    if (organizationData.slug) {
      const existingOrg = await prisma.organization.findFirst({
        where: {
          slug: organizationData.slug,
          id: { not: organizationId },
        },
      });

      if (existingOrg) {
        return {
          success: false,
          message: 'Organization slug already exists',
          statusCode: 409,
        };
      }
    }

    // Check if new domain is already taken (if provided)
    if (organizationData.domain) {
      const existingDomain = await prisma.organization.findFirst({
        where: {
          domain: organizationData.domain,
          id: { not: organizationId },
        },
      });

      if (existingDomain) {
        return {
          success: false,
          message: 'Domain already registered to another organization',
          statusCode: 409,
        };
      }
    }

    const updatedOrganization = await prisma.organization.update({
      where: { id: organizationId },
      data: organizationData,
    });

    return {
      success: true,
      data: updatedOrganization,
    };
  } catch (error) {
    logger.error('Update organization error:', error);
    return {
      success: false,
      message: 'Failed to update organization',
      statusCode: 500,
    };
  }
};

export const deleteOrganization = async (
  organizationId: string,
  userId: string
): Promise<ServiceResponse<null>> => {
  try {
    // Verify user belongs to this organization and has admin role
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user || user.organizationId !== organizationId) {
      return {
        success: false,
        message: 'Access denied to this organization',
        statusCode: 403,
      };
    }

    if (user.role !== 'ADMIN' && user.role !== 'SUPER_ADMIN') {
      return {
        success: false,
        message: 'Insufficient permissions to delete organization',
        statusCode: 403,
      };
    }

    // Soft delete the organization
    await prisma.organization.update({
      where: { id: organizationId },
      data: {
        isDeleted: true,
        deletedAt: new Date(),
        isActive: false,
      },
    });

    return {
      success: true,
      data: null,
    };
  } catch (error) {
    logger.error('Delete organization error:', error);
    return {
      success: false,
      message: 'Failed to delete organization',
      statusCode: 500,
    };
  }
};
