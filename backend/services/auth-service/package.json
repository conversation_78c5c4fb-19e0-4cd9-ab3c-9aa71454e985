{"name": "@crm/auth-service", "version": "1.0.0", "description": "Authentication service for CRM application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "clean": "rm -rf dist"}, "dependencies": {"@crm/database": "*", "@crm/shared": "*", "bcryptjs": "^2.4.3", "dotenv": "^16.3.0", "express": "^4.18.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/express": "^4.17.0", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@types/supertest": "^2.0.16", "jest": "^29.0.0", "jest-mock-extended": "^4.0.0-beta1", "nodemon": "^3.0.0", "supertest": "^6.3.4", "ts-jest": "^29.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}