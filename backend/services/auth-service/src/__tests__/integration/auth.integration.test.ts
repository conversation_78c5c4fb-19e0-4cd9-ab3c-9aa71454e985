import request from 'supertest';
import app from '../../index';
import { testDb, setupTestData, cleanupTestData } from './setup';

// Set test environment
process.env.NODE_ENV = 'test';

describe('Auth Integration Tests', () => {
  let testData: any;

  beforeAll(async () => {
    testData = await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('POST /login', () => {
    it('should successfully login with valid credentials', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        organizationId: testData.testOrganization.id,
      });
      expect(response.body.data.user.organization).toMatchObject({
        name: 'Test Organization',
        slug: 'test-org',
      });
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
    });

    it('should fail login with invalid email', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should fail login with invalid password', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should fail login with inactive user', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Account is deactivated');
    });

    it('should fail login with user from inactive organization', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Organization is inactive');
    });

    it('should fail login with missing credentials', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
        })
        .expect(422);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /register', () => {
    let adminToken: string;

    beforeAll(async () => {
      // Login as admin to get token for registration
      const loginResponse = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });

      if (loginResponse.body.success) {
        adminToken = loginResponse.body.data.accessToken;
      }
    });

    it('should successfully register new user', async () => {
      console.log('Admin token status:', adminToken ? 'exists' : 'missing');
      if (!adminToken) {
        console.log('Skipping registration test - admin login failed');
        return;
      }

      const response = await request(app)
        .post('/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          email: '<EMAIL>',
          password: 'NewPassword123!',
          firstName: 'New',
          lastName: 'User',
          organizationId: testData.testOrganization.id,
          role: 'AGENT',
        });

      if (response.status !== 201) {
        console.log('Registration failed:', response.status, response.body);
        console.log('Admin token:', adminToken ? 'exists' : 'missing');
        console.log('Request data:', {
          email: '<EMAIL>',
          password: 'newpassword123',
          firstName: 'New',
          lastName: 'User',
          organizationId: testData.testOrganization.id,
          role: 'AGENT',
        });
      }

      expect(response.status).toBe(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        role: 'AGENT',
        organizationId: testData.testOrganization.id,
      });

      // Verify user was created in database
      const createdUser = await testDb.user.findUnique({
        where: {
          email_organizationId: {
            email: '<EMAIL>',
            organizationId: testData.testOrganization.id,
          },
        },
      });
      expect(createdUser).toBeTruthy();
    });

    it('should fail registration with existing email in same organization', async () => {
      if (!adminToken) {
        console.log('Skipping registration test - admin login failed');
        return;
      }

      const response = await request(app)
        .post('/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          email: '<EMAIL>',
          password: 'Password123!',
          firstName: 'Duplicate',
          lastName: 'User',
          organizationId: testData.testOrganization.id,
          role: 'AGENT',
        });

      if (response.status !== 409) {
        console.log('Duplicate email test failed:', response.status, response.body);
        console.log('Organization ID used:', testData.testOrganization.id);
        console.log('Admin token exists:', adminToken ? 'yes' : 'no');
      }

      expect(response.status).toBe(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('User with this email already exists in this organization');
    });

    it('should fail registration with invalid organization', async () => {
      if (!adminToken) {
        console.log('Skipping registration test - admin login failed');
        return;
      }

      const response = await request(app)
        .post('/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'New',
          lastName: 'User',
          organizationId: 'invalid-org-id',
          role: 'AGENT',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid or inactive organization');
    });

    it('should fail registration with inactive organization', async () => {
      if (!adminToken) {
        console.log('Skipping registration test - admin login failed');
        return;
      }

      const response = await request(app)
        .post('/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          email: '<EMAIL>',
          password: 'password123',
          firstName: 'New',
          lastName: 'User',
          organizationId: testData.inactiveOrganization.id,
          role: 'AGENT',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid or inactive organization');
    });

    it('should fail registration with weak password', async () => {
      if (!adminToken) {
        console.log('Skipping registration test - admin login failed');
        return;
      }

      const response = await request(app)
        .post('/register')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          email: '<EMAIL>',
          password: '123',
          firstName: 'New',
          lastName: 'User',
          organizationId: testData.testOrganization.id,
          role: 'AGENT',
        })
        .expect(422); // Validation error

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /me', () => {
    let accessToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });
      accessToken = loginResponse.body.data.accessToken;
    });

    it('should successfully get current user', async () => {
      const response = await request(app)
        .get('/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        organizationId: testData.testOrganization.id,
      });
      expect(response.body.data.organization).toMatchObject({
        name: 'Test Organization',
        slug: 'test-org',
      });
    });

    it('should fail without authentication token', async () => {
      const response = await request(app)
        .get('/me')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/me')
        .set('Authorization', 'Bearer invalid.token.here')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /refresh', () => {
    let refreshToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });
      refreshToken = loginResponse.body.data.refreshToken;
    });

    it('should successfully refresh tokens', async () => {
      const response = await request(app)
        .post('/refresh')
        .send({
          refreshToken,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.accessToken).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
    });

    it('should fail with invalid refresh token', async () => {
      const response = await request(app)
        .post('/refresh')
        .send({
          refreshToken: 'invalid.refresh.token',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid refresh token');
    });
  });

  describe('POST /change-password', () => {
    let accessToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });
      accessToken = loginResponse.body.data.accessToken;
    });

    it('should successfully change password', async () => {
      const response = await request(app)
        .put('/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'password123',
          newPassword: 'NewPassword123!',
        });

      if (response.status !== 200) {
        console.log('Change password failed:', response.status, response.body);
      }

      expect(response.status).toBe(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Password changed successfully');

      // Verify can login with new password
      const loginResponse = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'NewPassword123!',
        })
        .expect(200);

      expect(loginResponse.body.success).toBe(true);
    });

    it('should fail with incorrect current password', async () => {
      const response = await request(app)
        .put('/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'NewPassword456!',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Current password is incorrect');
    });
  });

  describe('POST /logout', () => {
    let accessToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });
      accessToken = loginResponse.body.data.accessToken;
    });

    it('should successfully logout', async () => {
      const response = await request(app)
        .post('/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Logout successful');
    });
  });
});
