import { PrismaClient } from '@crm/database';
import { PasswordHelper } from '@crm/shared';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://crm_user:crm_password@localhost:5432/crm_database';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

export const testDb = prisma;

// Test data setup
export const setupTestData = async () => {
  // Clean up existing test data
  await cleanupTestData();

  // Create test organization
  const testOrganization = await testDb.organization.create({
    data: {
      name: 'Test Organization',
      slug: 'test-org',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Test St',
      website: 'https://test.com',
      timezone: 'UTC',
      currency: 'USD',
    },
  });

  // Create inactive organization
  const inactiveOrganization = await testDb.organization.create({
    data: {
      name: 'Inactive Organization',
      slug: 'inactive-org',
      email: '<EMAIL>',
      timezone: 'UTC',
      currency: 'USD',
      isActive: false,
    },
  });

  // Create test users
  const hashedPassword = await PasswordHelper.hash('password123');

  const superAdmin = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      organizationId: testOrganization.id,
      role: 'SUPER_ADMIN',
      isVerified: true,
    },
  });

  const admin = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'ADMIN',
      isVerified: true,
    },
  });

  const agent = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Agent',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  const inactiveUser = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Inactive',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'AGENT',
      isActive: false,
      isVerified: true,
    },
  });

  const userWithInactiveOrg = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'User',
      lastName: 'InactiveOrg',
      organizationId: inactiveOrganization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  return {
    testOrganization,
    inactiveOrganization,
    superAdmin,
    admin,
    agent,
    inactiveUser,
    userWithInactiveOrg,
  };
};

export const cleanupTestData = async () => {
  try {
    // Delete in correct order due to foreign key constraints
    await testDb.user.deleteMany({
      where: {
        email: {
          in: [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ],
        },
      },
    });

    await testDb.organization.deleteMany({
      where: {
        slug: {
          in: ['test-org', 'inactive-org', 'new-test-org'],
        },
      },
    });
  } catch (error) {
    console.warn('Cleanup warning:', error);
    // Don't fail tests if cleanup fails
  }
};

// Setup and teardown hooks
beforeAll(async () => {
  await setupTestData();
});

afterAll(async () => {
  await cleanupTestData();
  await testDb.$disconnect();
});

export default testDb;
