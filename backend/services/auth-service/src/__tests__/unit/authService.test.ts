import { prismaMock } from '../mocks/prisma';
import * as authService from '../../services/authService';
import { JWTHelper, PasswordHelper } from '@crm/shared';
import {
  mockUser,
  mockOrganization,
  mockInactiveUser,
  mockUserWithInactiveOrg,
  mockRegisterRequest,
  mockTokens,
} from '../mocks/testData';

// Mock external dependencies
jest.mock('@crm/shared', () => ({
  ...jest.requireActual('@crm/shared'),
  JWTHelper: {
    generateTokenPair: jest.fn(),
  },
  PasswordHelper: {
    compare: jest.fn(),
    hash: jest.fn(),
    validatePasswordStrength: jest.fn(),
  },
  createServiceLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

const mockJWTHelper = JWTHelper as jest.Mocked<typeof JWTHelper>;
const mockPasswordHelper = PasswordHelper as jest.Mocked<typeof PasswordHelper>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('loginUser', () => {
    it('should successfully login a valid user', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      mockPasswordHelper.compare.mockResolvedValue(true);
      mockJWTHelper.generateTokenPair.mockReturnValue(mockTokens);

      // Act
      const result = await authService.loginUser(email, password);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        user: {
          id: mockUser.id,
          email: mockUser.email,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          role: mockUser.role,
          isActive: mockUser.isActive,
          organizationId: mockUser.organizationId,
          organization: mockUser.organization,
        },
        accessToken: mockTokens.accessToken,
        refreshToken: mockTokens.refreshToken,
      });
      expect(prismaMock.user.findUnique).toHaveBeenCalledWith({
        where: { email },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
              isActive: true,
            },
          },
        },
      });
    });

    it('should fail login with invalid email', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      prismaMock.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await authService.loginUser(email, password);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Invalid email or password');
      expect(result.statusCode).toBe(401);
    });

    it('should fail login with inactive user', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      prismaMock.user.findUnique.mockResolvedValue(mockInactiveUser as any);

      // Act
      const result = await authService.loginUser(email, password);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Account is deactivated');
      expect(result.statusCode).toBe(401);
    });

    it('should fail login with inactive organization', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      prismaMock.user.findUnique.mockResolvedValue(mockUserWithInactiveOrg as any);

      // Act
      const result = await authService.loginUser(email, password);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Organization is inactive');
      expect(result.statusCode).toBe(401);
    });

    it('should fail login with invalid password', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'wrongpassword';

      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      mockPasswordHelper.compare.mockResolvedValue(false);

      // Act
      const result = await authService.loginUser(email, password);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Invalid email or password');
      expect(result.statusCode).toBe(401);
    });

    it('should handle database errors', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      prismaMock.user.findUnique.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await authService.loginUser(email, password);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Login failed');
      expect(result.statusCode).toBe(500);
    });
  });

  describe('registerUser', () => {
    it('should successfully register a new user', async () => {
      // Arrange
      prismaMock.organization.findUnique.mockResolvedValue(mockOrganization as any);
      prismaMock.user.findUnique.mockResolvedValue(null); // No existing user
      mockPasswordHelper.hash.mockResolvedValue('hashedpassword');
      mockPasswordHelper.validatePasswordStrength.mockReturnValue({ isValid: true, errors: [] });

      const newUser = {
        ...mockUser,
        id: 'new-user-123',
        email: mockRegisterRequest.email,
        firstName: mockRegisterRequest.firstName,
        lastName: mockRegisterRequest.lastName,
      };
      prismaMock.user.create.mockResolvedValue(newUser as any);

      // Act
      const result = await authService.registerUser(mockRegisterRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(newUser);
      expect(prismaMock.organization.findUnique).toHaveBeenCalledWith({
        where: { id: mockRegisterRequest.organizationId },
      });
    });

    it('should fail registration with invalid organization', async () => {
      // Arrange
      prismaMock.organization.findUnique.mockResolvedValue(null);

      // Act
      const result = await authService.registerUser(mockRegisterRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Invalid or inactive organization');
      expect(result.statusCode).toBe(400);
    });

    it('should fail registration with existing user email in organization', async () => {
      // Arrange
      prismaMock.organization.findUnique.mockResolvedValue(mockOrganization as any);
      prismaMock.user.findUnique.mockResolvedValue(mockUser as any); // Existing user

      // Act
      const result = await authService.registerUser(mockRegisterRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('User with this email already exists in this organization');
      expect(result.statusCode).toBe(409);
    });

    it('should fail registration with weak password', async () => {
      // Arrange
      prismaMock.organization.findUnique.mockResolvedValue(mockOrganization as any);
      prismaMock.user.findUnique.mockResolvedValue(null);
      mockPasswordHelper.validatePasswordStrength.mockReturnValue({
        isValid: false,
        errors: ['Password too weak'],
      });

      // Act
      const result = await authService.registerUser(mockRegisterRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Password too weak');
      expect(result.statusCode).toBe(400);
    });
  });

  describe('refreshUserToken', () => {
    it('should successfully refresh tokens for valid user', async () => {
      // Arrange
      const refreshToken = 'valid.refresh.token';
      const mockDecoded = { userId: 'user-123' };

      mockJWTHelper.verifyRefreshToken = jest.fn().mockReturnValue(mockDecoded);
      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      mockJWTHelper.generateTokenPair.mockReturnValue(mockTokens);

      // Act
      const result = await authService.refreshUserToken(refreshToken);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockTokens);
    });

    it('should fail refresh with invalid token', async () => {
      // Arrange
      const refreshToken = 'invalid.refresh.token';

      mockJWTHelper.verifyRefreshToken = jest.fn().mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act
      const result = await authService.refreshUserToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Invalid refresh token');
      expect(result.statusCode).toBe(401);
    });

    it('should fail refresh with inactive user', async () => {
      // Arrange
      const refreshToken = 'valid.refresh.token';
      const mockDecoded = { userId: 'inactive-user-123' };

      mockJWTHelper.verifyRefreshToken = jest.fn().mockReturnValue(mockDecoded);
      prismaMock.user.findUnique.mockResolvedValue(mockInactiveUser as any);

      // Act
      const result = await authService.refreshUserToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Invalid refresh token');
      expect(result.statusCode).toBe(401);
    });

    it('should fail refresh with inactive organization', async () => {
      // Arrange
      const refreshToken = 'valid.refresh.token';
      const mockDecoded = { userId: 'user-inactive-org-123' };

      mockJWTHelper.verifyRefreshToken = jest.fn().mockReturnValue(mockDecoded);
      prismaMock.user.findUnique.mockResolvedValue(mockUserWithInactiveOrg as any);

      // Act
      const result = await authService.refreshUserToken(refreshToken);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Organization is inactive');
      expect(result.statusCode).toBe(401);
    });
  });

  describe('changeUserPassword', () => {
    it('should successfully change password', async () => {
      // Arrange
      const userId = 'user-123';
      const currentPassword = 'oldpassword';
      const newPassword = 'newpassword123';

      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      mockPasswordHelper.compare.mockResolvedValue(true);
      mockPasswordHelper.validatePasswordStrength.mockReturnValue({ isValid: true, errors: [] });
      mockPasswordHelper.hash.mockResolvedValue('newhashed');
      prismaMock.user.update.mockResolvedValue(mockUser as any);

      // Act
      const result = await authService.changeUserPassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(null);
    });

    it('should fail with user not found', async () => {
      // Arrange
      const userId = 'nonexistent-user';
      const currentPassword = 'oldpassword';
      const newPassword = 'newpassword123';

      prismaMock.user.findUnique.mockResolvedValue(null);

      // Act
      const result = await authService.changeUserPassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('User not found');
      expect(result.statusCode).toBe(404);
    });

    it('should fail with incorrect current password', async () => {
      // Arrange
      const userId = 'user-123';
      const currentPassword = 'wrongpassword';
      const newPassword = 'newpassword123';

      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      mockPasswordHelper.compare.mockResolvedValue(false);

      // Act
      const result = await authService.changeUserPassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Current password is incorrect');
      expect(result.statusCode).toBe(400);
    });

    it('should fail with weak new password', async () => {
      // Arrange
      const userId = 'user-123';
      const currentPassword = 'oldpassword';
      const newPassword = 'weak';

      prismaMock.user.findUnique.mockResolvedValue(mockUser as any);
      mockPasswordHelper.compare.mockResolvedValue(true);
      mockPasswordHelper.validatePasswordStrength.mockReturnValue({
        isValid: false,
        errors: ['Password too short'],
      });

      // Act
      const result = await authService.changeUserPassword(userId, currentPassword, newPassword);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Password too short');
      expect(result.statusCode).toBe(400);
    });
  });
});
