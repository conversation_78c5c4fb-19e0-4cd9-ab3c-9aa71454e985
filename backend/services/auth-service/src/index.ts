import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  authLimiter,
  createServiceLogger,
} from '@crm/shared';
import authRoutes from './routes/auth';

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const app = express();
const PORT = process.env.AUTH_SERVICE_PORT || 3001;
const logger = createServiceLogger('auth-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting for auth endpoints (disabled in test environment)
if (process.env.NODE_ENV !== 'test') {
  app.use(authLimiter);
}

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'auth-service',
    version: '1.0.0',
  });
});

// Routes
app.use('/', authRoutes);

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🔐 Auth Service running on port ${PORT}`);
});

export default app;
