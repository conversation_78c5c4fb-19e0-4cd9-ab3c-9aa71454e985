{"name": "@crm/properties-service", "version": "1.0.0", "description": "Property management service for CRM application", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "clean": "rm -rf dist"}, "dependencies": {"@crm/database": "", "@crm/shared": "", "dotenv": "^16.3.0", "express": "^4.18.0", "joi": "^17.9.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.12", "@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.1.2", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0"}}