import { Response } from 'express';
import { prisma } from '@crm/database';
import { AuthenticatedRequest, ResponseHelper, createServiceLogger } from '@crm/shared';
import { PropertyService } from '../services/propertyService';
import { CreatePropertyInput, UpdatePropertyInput } from '../types/property';

const logger = createServiceLogger('properties-controller');

export const createProperty = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    // Get user's organization ID from database
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const input: CreatePropertyInput = {
      ...req.body,
      organizationId: user.organizationId,
    };

    const result = await PropertyService.createProperty(input);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.message || 'Failed to create property'
      });
      return;
    }

    logger.info(`Property created by ${req.user.id}`);
    ResponseHelper.success(res, result.data, 'Property created successfully', 201);
  } catch (error) {
    logger.error('Error creating property:', error);
    ResponseHelper.error(res, 'Failed to create property', 500);
  }
};

export const getAllProperties = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    // Parse query parameters
    const {
      page = 1,
      limit = 20,
      search,
      city,
      state,
      zipCode,
      propertyType,
      status,
      assigneeId,
      minSquareMeters,
      maxSquareMeters,
      minLotSize,
      maxLotSize,
      minBedrooms,
      maxBedrooms,
      minBathrooms,
      maxBathrooms,
      minPrice,
      maxPrice,
      priceType,
      listedAfter,
      listedBefore,
      updatedAfter,
      updatedBefore,
      features,
      amenities,
      isFeatured,
      isActive,
      sortBy,
      sortOrder
    } = req.query;

    // Parse array parameters
    const parsedFeatures = features ?
      (Array.isArray(features) ? features as string[] : [features as string]) :
      undefined;
    const parsedAmenities = amenities ?
      (Array.isArray(amenities) ? amenities as string[] : [amenities as string]) :
      undefined;

    const result = await PropertyService.getProperties({
      organizationId: user.organizationId,
      page: Number(page),
      limit: Number(limit),
      search: search as string,
      city: city as string,
      state: state as string,
      zipCode: zipCode as string,
      propertyType: propertyType as any,
      status: status as any,
      assigneeId: assigneeId as string,
      minSquareMeters: minSquareMeters ? Number(minSquareMeters) : undefined,
      maxSquareMeters: maxSquareMeters ? Number(maxSquareMeters) : undefined,
      minLotSize: minLotSize ? Number(minLotSize) : undefined,
      maxLotSize: maxLotSize ? Number(maxLotSize) : undefined,
      minBedrooms: minBedrooms ? Number(minBedrooms) : undefined,
      maxBedrooms: maxBedrooms ? Number(maxBedrooms) : undefined,
      minBathrooms: minBathrooms ? Number(minBathrooms) : undefined,
      maxBathrooms: maxBathrooms ? Number(maxBathrooms) : undefined,
      minPrice: minPrice ? Number(minPrice) : undefined,
      maxPrice: maxPrice ? Number(maxPrice) : undefined,
      priceType: priceType as any,
      listedAfter: listedAfter ? new Date(listedAfter as string) : undefined,
      listedBefore: listedBefore ? new Date(listedBefore as string) : undefined,
      updatedAfter: updatedAfter ? new Date(updatedAfter as string) : undefined,
      updatedBefore: updatedBefore ? new Date(updatedBefore as string) : undefined,
      features: parsedFeatures,
      amenities: parsedAmenities,
      isFeatured: isFeatured ? isFeatured === 'true' : undefined,
      isActive: isActive ? isActive === 'true' : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc'
    });

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.message || 'Failed to fetch properties'
      });
      return;
    }

    ResponseHelper.success(res, result.data, 'Properties fetched successfully');
  } catch (error) {
    logger.error('Error fetching properties:', error);
    ResponseHelper.error(res, 'Failed to fetch properties', 500);
  }
};

export const getPropertyById = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const result = await PropertyService.getPropertyById(
      req.params.id,
      user.organizationId
    );

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.message || 'Failed to fetch property'
      });
      return;
    }

    ResponseHelper.success(res, result.data, 'Property fetched successfully');
  } catch (error) {
    logger.error('Error fetching property:', error);
    ResponseHelper.error(res, 'Failed to fetch property', 500);
  }
};

export const updateProperty = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const input: UpdatePropertyInput = req.body;

    const result = await PropertyService.updateProperty(
      req.params.id,
      user.organizationId,
      input
    );

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.message || 'Failed to update property'
      });
      return;
    }

    logger.info(`Property ${req.params.id} updated by ${req.user.id}`);
    ResponseHelper.success(res, result.data, 'Property updated successfully');
  } catch (error) {
    logger.error('Error updating property:', error);
    ResponseHelper.error(res, 'Failed to update property', 500);
  }
};

export const deleteProperty = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const result = await PropertyService.deleteProperty(
      req.params.id,
      user.organizationId
    );

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.message || 'Failed to delete property'
      });
      return;
    }

    logger.info(`Property ${req.params.id} deleted by ${req.user.id}`);
    ResponseHelper.success(res, null, 'Property deleted successfully');
  } catch (error) {
    logger.error('Error deleting property:', error);
    ResponseHelper.error(res, 'Failed to delete property', 500);
  }
};

export const searchProperties = async (
  req: AuthenticatedRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { organizationId: true }
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    const {
      query,
      page = 1,
      limit = 20,
      propertyType,
      status,
      minPrice,
      maxPrice,
      city,
      state
    } = req.query;

    if (!query) {
      ResponseHelper.badRequest(res, 'Search query is required');
      return;
    }

    const result = await PropertyService.searchProperties({
      query: query as string,
      organizationId: user.organizationId,
      page: Number(page),
      limit: Number(limit),
      propertyType: propertyType as any,
      status: status as any,
      minPrice: minPrice ? Number(minPrice) : undefined,
      maxPrice: maxPrice ? Number(maxPrice) : undefined,
      city: city as string,
      state: state as string,
    });

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.message || 'Failed to search properties'
      });
      return;
    }

    ResponseHelper.success(res, result.data, 'Properties searched successfully');
  } catch (error) {
    logger.error('Error searching properties:', error);
    ResponseHelper.error(res, 'Failed to search properties', 500);
  }
};
