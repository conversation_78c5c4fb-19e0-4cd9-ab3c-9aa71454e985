import { prisma } from '@crm/database';
import { PropertyType, PropertyStatus, UserRole } from '@prisma/client';

export const testDb = prisma;

export const setupTestData = async () => {
  // Create test organizations
  const organization1 = await prisma.organization.create({
    data: {
      name: 'Test Real Estate Co',
      slug: 'test-real-estate-co',
      email: '<EMAIL>',
    },
  });

  const organization2 = await prisma.organization.create({
    data: {
      name: 'Another Real Estate Co',
      slug: 'another-real-estate-co',
      email: '<EMAIL>',
    },
  });

  // Create test users
  const agent = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Test',
      lastName: 'Agent',
      role: UserRole.AGENT,
      organizationId: organization1.id,
      isActive: true,
      isVerified: true,
    },
  });

  const anotherAgent = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Another',
      lastName: 'Agent',
      role: UserRole.AGENT,
      organizationId: organization2.id,
      isActive: true,
      isVerified: true,
    },
  });

  const manager = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Test',
      lastName: 'Manager',
      role: UserRole.MANAGER,
      organizationId: organization1.id,
      isActive: true,
      isVerified: true,
    },
  });

  // Create test properties
  const property1 = await prisma.property.create({
    data: {
      title: 'Beautiful Family Home',
      description: 'A stunning 3-bedroom family home with modern amenities',
      propertyType: PropertyType.SINGLE_FAMILY,
      status: PropertyStatus.AVAILABLE,
      address: '123 Main Street',
      city: 'Springfield',
      state: 'IL',
      zipCode: '62701',
      country: 'US',
      bedrooms: 3,
      bathrooms: 2.5,
      squareMeters: 200,
      lotSizeMeters: 800,
      yearBuilt: 2015,
      listPrice: 350000,
      features: ['garage', 'garden', 'fireplace'],
      amenities: ['pool', 'gym'],
      images: ['https://example.com/image1.jpg'],
      mlsNumber: 'MLS123456',
      listingDate: new Date('2024-01-01'),
      isFeatured: true,
      organizationId: organization1.id,
      assigneeId: agent.id,
    },
  });

  const property2 = await prisma.property.create({
    data: {
      title: 'Modern Condo Downtown',
      description: 'Luxury condo in the heart of downtown',
      propertyType: PropertyType.CONDO,
      status: PropertyStatus.UNDER_CONTRACT,
      address: '456 Oak Avenue',
      city: 'Springfield',
      state: 'IL',
      zipCode: '62702',
      country: 'US',
      bedrooms: 2,
      bathrooms: 2,
      squareMeters: 120,
      yearBuilt: 2020,
      listPrice: 280000,
      salePrice: 275000,
      features: ['balcony', 'parking'],
      amenities: ['concierge', 'rooftop'],
      images: ['https://example.com/image2.jpg'],
      mlsNumber: 'MLS789012',
      listingDate: new Date('2024-01-15'),
      isFeatured: false,
      organizationId: organization1.id,
      assigneeId: manager.id,
    },
  });

  const property3 = await prisma.property.create({
    data: {
      title: 'Spacious Townhouse',
      description: 'Large townhouse perfect for families',
      propertyType: PropertyType.TOWNHOUSE,
      status: PropertyStatus.AVAILABLE,
      address: '789 Pine Street',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60601',
      country: 'US',
      bedrooms: 4,
      bathrooms: 3,
      squareMeters: 250,
      lotSizeMeters: 400,
      yearBuilt: 2010,
      listPrice: 450000,
      features: ['garage', 'basement'],
      amenities: ['playground'],
      images: ['https://example.com/image3.jpg'],
      mlsNumber: 'MLS345678',
      listingDate: new Date('2024-02-01'),
      isFeatured: true,
      organizationId: organization1.id,
    },
  });

  // Property from different organization
  const propertyFromAnotherOrg = await prisma.property.create({
    data: {
      title: 'Another Org Property',
      description: 'Property from another organization',
      propertyType: PropertyType.SINGLE_FAMILY,
      status: PropertyStatus.AVAILABLE,
      address: '999 Different Street',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60602',
      country: 'US',
      bedrooms: 3,
      bathrooms: 2,
      squareMeters: 180,
      yearBuilt: 2018,
      listPrice: 320000,
      features: ['garage'],
      amenities: ['pool'],
      organizationId: organization2.id,
      assigneeId: anotherAgent.id,
    },
  });

  return {
    organization1,
    organization2,
    agent,
    anotherAgent,
    manager,
    property1,
    property2,
    property3,
    propertyFromAnotherOrg,
  };
};

export const cleanupTestData = async () => {
  // Clean up in reverse order of dependencies
  await prisma.property.deleteMany({});
  await prisma.user.deleteMany({});
  await prisma.organization.deleteMany({});
};
