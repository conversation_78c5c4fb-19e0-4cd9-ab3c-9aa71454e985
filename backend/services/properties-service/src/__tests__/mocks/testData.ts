import { PropertyType, PropertyStatus, UserRole } from '@prisma/client';

export const mockOrganization = {
  id: 'org-1',
  name: 'Test Real Estate Co',
  slug: 'test-real-estate-co',
  email: '<EMAIL>',
  isActive: true,
  isDeleted: false,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'Agent',
  role: UserRole.AGENT,
  organizationId: 'org-1',
  isActive: true,
  isVerified: true,
  isDeleted: false,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockManager = {
  id: 'user-2',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'Manager',
  role: UserRole.MANAGER,
  organizationId: 'org-1',
  isActive: true,
  isVerified: true,
  isDeleted: false,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockProperty = {
  id: 'property-1',
  title: 'Beautiful Family Home',
  description: 'A stunning 3-bedroom family home with modern amenities',
  propertyType: PropertyType.SINGLE_FAMILY,
  status: PropertyStatus.AVAILABLE,
  address: '123 Main Street',
  city: 'Springfield',
  state: 'IL',
  zipCode: '62701',
  country: 'US',
  latitude: 39.7817,
  longitude: -89.6501,
  bedrooms: 3,
  bathrooms: 2.5,
  squareMeters: 200,
  lotSizeMeters: 800,
  yearBuilt: 2015,
  listPrice: 350000,
  salePrice: null,
  rentPrice: null,
  pricePerSquareMeter: 1750,
  assigneeId: 'user-1',
  features: ['garage', 'garden', 'fireplace'],
  amenities: ['pool', 'gym'],
  images: ['https://example.com/image1.jpg'],
  virtualTourUrl: 'https://example.com/tour1',
  mlsNumber: 'MLS123456',
  listingDate: new Date('2024-01-01'),
  expirationDate: new Date('2024-12-31'),
  daysOnMarket: 30,
  isActive: true,
  isFeatured: true,
  isDeleted: false,
  deletedAt: null,
  organizationId: 'org-1',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockCondoProperty = {
  id: 'property-2',
  title: 'Modern Condo Downtown',
  description: 'Luxury condo in the heart of downtown',
  propertyType: PropertyType.CONDO,
  status: PropertyStatus.UNDER_CONTRACT,
  address: '456 Oak Avenue',
  city: 'Springfield',
  state: 'IL',
  zipCode: '62702',
  country: 'US',
  latitude: 39.7817,
  longitude: -89.6501,
  bedrooms: 2,
  bathrooms: 2,
  squareMeters: 120,
  lotSizeMeters: null,
  yearBuilt: 2020,
  listPrice: 280000,
  salePrice: 275000,
  rentPrice: null,
  pricePerSquareMeter: 2333,
  assigneeId: 'user-2',
  features: ['balcony', 'parking'],
  amenities: ['concierge', 'rooftop'],
  images: ['https://example.com/image2.jpg'],
  virtualTourUrl: null,
  mlsNumber: 'MLS789012',
  listingDate: new Date('2024-01-15'),
  expirationDate: new Date('2024-12-31'),
  daysOnMarket: 15,
  isActive: true,
  isFeatured: false,
  isDeleted: false,
  deletedAt: null,
  organizationId: 'org-1',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockCreatePropertyRequest = {
  title: 'New Test Property',
  description: 'A newly created test property',
  propertyType: PropertyType.SINGLE_FAMILY,
  status: PropertyStatus.AVAILABLE,
  address: '999 Test Street',
  city: 'Test City',
  state: 'TS',
  zipCode: '12345',
  bedrooms: 3,
  bathrooms: 2,
  squareMeters: 150,
  listPrice: 300000,
  features: ['garage', 'garden'],
  amenities: ['pool'],
  assigneeId: 'user-1',
};

export const mockUpdatePropertyRequest = {
  title: 'Updated Property Title',
  description: 'Updated description',
  listPrice: 375000,
  bedrooms: 4,
  features: ['garage', 'garden', 'pool'],
};

export const mockPropertyFilterParams = {
  organizationId: 'org-1',
  page: 1,
  limit: 20,
  city: 'Springfield',
  propertyType: PropertyType.SINGLE_FAMILY,
  status: PropertyStatus.AVAILABLE,
  minPrice: 300000,
  maxPrice: 400000,
  minBedrooms: 3,
  maxBedrooms: 4,
  isFeatured: true,
  sortBy: 'price',
  sortOrder: 'asc' as const,
};

export const mockSearchParams = {
  query: 'family home',
  organizationId: 'org-1',
  page: 1,
  limit: 20,
  propertyType: PropertyType.SINGLE_FAMILY,
  minPrice: 300000,
  maxPrice: 400000,
};

export const mockPaginationResponse = {
  page: 1,
  limit: 20,
  total: 2,
  totalPages: 1,
  hasNext: false,
  hasPrev: false,
};
