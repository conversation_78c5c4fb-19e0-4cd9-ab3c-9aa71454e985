import Joi from 'joi';
import { PropertyType, PropertyStatus } from '@prisma/client';

// Create Property Schema
export const createPropertySchema = Joi.object({
  title: Joi.string().required(),
  description: Joi.string().optional(),
  propertyType: Joi.string().valid(...Object.values(PropertyType)).required(),
  status: Joi.string().valid(...Object.values(PropertyStatus)).optional(),
  
  // Location
  address: Joi.string().required(),
  city: Joi.string().required(),
  state: Joi.string().required(),
  zipCode: Joi.string().optional(),
  country: Joi.string().length(2).optional(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional(),
  
  // Property details
  bedrooms: Joi.number().min(0).optional(),
  bathrooms: Joi.number().min(0).optional(),
  squareMeters: Joi.number().min(0).optional(),
  lotSizeMeters: Joi.number().min(0).optional(),
  yearBuilt: Joi.number().min(1800).max(new Date().getFullYear() + 5).optional(),
  
  // Pricing
  listPrice: Joi.number().min(0).optional(),
  salePrice: Joi.number().min(0).optional(),
  rentPrice: Joi.number().min(0).optional(),
  pricePerSquareMeter: Joi.number().min(0).optional(),
  
  // Assignment
  assigneeId: Joi.string().optional(),
  
  // Features & amenities
  features: Joi.array().items(Joi.string()).optional(),
  amenities: Joi.array().items(Joi.string()).optional(),
  
  // Media
  images: Joi.array().items(Joi.string().uri()).optional(),
  virtualTourUrl: Joi.string().uri().optional(),
  
  // Listing details
  mlsNumber: Joi.string().optional(),
  listingDate: Joi.date().optional(),
  expirationDate: Joi.date().optional(),
  daysOnMarket: Joi.number().min(0).optional(),
  
  // Status
  isFeatured: Joi.boolean().optional()
});

// Update Property Schema
export const updatePropertySchema = createPropertySchema.fork(
  ['title', 'propertyType', 'address', 'city', 'state'],
  (field) => field.optional()
);

// Query validation (GET /properties)
export const getPropertiesQuerySchema = Joi.object({
  page: Joi.number().min(1).optional(),
  limit: Joi.number().min(1).max(100).optional(),
  search: Joi.string().optional(),
  
  // Location filters
  city: Joi.string().optional(),
  state: Joi.string().optional(),
  zipCode: Joi.string().optional(),
  
  // Property filters
  propertyType: Joi.string().valid(...Object.values(PropertyType)).optional(),
  status: Joi.string().valid(...Object.values(PropertyStatus)).optional(),
  assigneeId: Joi.string().optional(),
  
  // Size filters
  minSquareMeters: Joi.number().min(0).optional(),
  maxSquareMeters: Joi.number().min(0).optional(),
  minLotSize: Joi.number().min(0).optional(),
  maxLotSize: Joi.number().min(0).optional(),
  
  // Bedroom/bathroom filters
  minBedrooms: Joi.number().min(0).optional(),
  maxBedrooms: Joi.number().min(0).optional(),
  minBathrooms: Joi.number().min(0).optional(),
  maxBathrooms: Joi.number().min(0).optional(),
  
  // Price filters
  minPrice: Joi.number().min(0).optional(),
  maxPrice: Joi.number().min(0).optional(),
  priceType: Joi.string().valid('listPrice', 'salePrice', 'rentPrice').optional(),
  
  // Date filters
  listedAfter: Joi.date().optional(),
  listedBefore: Joi.date().optional(),
  updatedAfter: Joi.date().optional(),
  updatedBefore: Joi.date().optional(),
  
  // Feature filters
  features: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ).optional(),
  amenities: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ).optional(),
  
  // Status filters
  isFeatured: Joi.boolean().optional(),
  isActive: Joi.boolean().optional(),
  
  // Sorting
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').optional()
});

// Search validation (GET /properties/search)
export const searchPropertiesQuerySchema = Joi.object({
  query: Joi.string().required(),
  page: Joi.number().min(1).optional(),
  limit: Joi.number().min(1).max(100).optional(),
  propertyType: Joi.string().valid(...Object.values(PropertyType)).optional(),
  status: Joi.string().valid(...Object.values(PropertyStatus)).optional(),
  minPrice: Joi.number().min(0).optional(),
  maxPrice: Joi.number().min(0).optional(),
  city: Joi.string().optional(),
  state: Joi.string().optional()
});
