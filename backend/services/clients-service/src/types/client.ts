import {ClientType , ClientStatus , LeadSource , PropertyType} from '@prisma/client'

export interface CreateClientInput {
firstName: string;
lastName: string;
email?: string;
phone?: string;
alternatePhone?: string;
type?: ClientType;
status?: ClientStatus;
source?: LeadSource;
address?: string;
city?: string;
state?: string;
zipCode?: string;
country?: string;
budget?: number;
preferredAreas?: string[];
propertyTypes?: PropertyType[];
notes?: string;
tags?: string[];
consentGiven?: boolean;
consentDate?: Date;
marketingOptIn?: boolean;
organizationId: string;
ownerId: string;
}

export type UpdateClientInput = Partial<Omit<CreateClientInput, 'organizationId' | 'ownerId'>>;

export interface SearchClientQuery {
page?: string;
limit?: string;
search?: string;
status?: ClientStatus;
type?: ClientType;
source?: LeadSource;
tags?: string;
sortBy?: string;
sortOrder?: 'asc' | 'desc';
}