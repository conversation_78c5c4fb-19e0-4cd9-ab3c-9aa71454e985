import { Router } from 'express';
import {
  validateBody,
  authenticateToken,
  asyncHandler,
  requireAgent,
  validateQuery,
} from '@crm/shared';
import * as ClientController from '../controllers/clientController';
import { createClientSchema, getClientsQuerySchema, updateClientSchema, searchClientsQuerySchema } from '../utils/validation/clientSchema';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

router.get(
  '/',
  validateQuery(getClientsQuerySchema),
  asyncHandler(ClientController.getAllClients)
);

router.post(
  '/',
  requireAgent,
  validateBody(createClientSchema),
  asyncHand<PERSON>(ClientController.createClient)
);

router.get(
  '/search',
  requireAgent,
  validateQuery(searchClientsQuerySchema),
  async<PERSON>and<PERSON>(ClientController.searchClients)
);

router.get(
  '/:id',
  asyncHandler(ClientController.getClientById)
);

router.put(
  '/:id',
  requireAgent,
  validate<PERSON><PERSON>(updateClientSchema),
  async<PERSON><PERSON><PERSON>(ClientController.updateClient)
);

router.delete(
  '/:id',
  requireAgent,
  asyncHandler(ClientController.deleteClient)
);

export default router;
