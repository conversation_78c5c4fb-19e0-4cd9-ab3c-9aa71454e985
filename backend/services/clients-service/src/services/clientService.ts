import { prisma } from '@crm/database';
import { ServiceResponse, createServiceLogger } from '@crm/shared';
import { Client, Prisma } from '@prisma/client';
import { CreateClientInput } from '../types/client';

const logger = createServiceLogger('client-service');

interface ClientFilterParams {
  organizationId: string;
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  clientType?: string;
  source?: string;
  assignedAgentId?: string;
  tags?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const ClientService = {

  //* Create a new client
  async createClient(input: CreateClientInput): Promise<ServiceResponse<Client>> {
    try {
      const existing = await prisma.client.findFirst({
        where: {
          email: input.email,
          organizationId: input.organizationId,
          isDeleted: false,
        },
      });
      if (existing) {
        return {
          success: false,
          message: 'Client with this email already exists',
          statusCode: 409,
        };
      }

      const { organizationId, ownerId, ...data } = input;
      const client = await prisma.client.create({
        data: {
          ...data,
          organization: { connect: { id: organizationId } },
          owner: { connect: { id: ownerId } }
        }
      });

      return {
        success: true,
        data: client
      };
    } catch (error) {
      logger.error('Create client error:', error);
      return {
        success: false,
        message: 'Failed to create client',
        statusCode: 500
      };
    }
  },
  //* Find client by ID and organizationId
  async getClientById(id: string, organizationId: string): Promise<ServiceResponse<Client>> {
    try {
      const client = await prisma.client.findUnique({
        where: {
          id,
          organizationId,
          isDeleted: false
        }
      });

      if (!client) {
        return {
          success: false,
          message: 'Client not found',
          statusCode: 404
        };
      }

      return {
        success: true,
        data: client
      };
    } catch (error) {
      logger.error('Get client by ID error:', error);
      return {
        success: false,
        message: 'Failed to fetch client',
        statusCode: 500
      };
    }
  },
  //* Find all clients 
  async getClients(
    filterParams: ClientFilterParams
  ): Promise<ServiceResponse<{ clients: Client[]; pagination: any }>> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        status,
        clientType,
        source,
        assignedAgentId,
        tags,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filterParams;

      // Ensure limit doesn't exceed 100
      const validatedLimit = Math.min(limit, 100);
      const skip = (page - 1) * validatedLimit;

      // Build where clause
      const where: any = {
        isDeleted: false,
        organizationId: filterParams.organizationId
      };

      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (clientType) {
        where.type = clientType;
      }

      if (source) {
        where.source = source;
      }

      if (assignedAgentId) {
        where.assignedAgentId = assignedAgentId;
      }

      if (tags) {
        const tagArray = tags.split(',').map(tag => tag.trim());
        where.tags = {
          hasSome: tagArray
        };
      }

      const [clients, total] = await Promise.all([
        prisma.client.findMany({
          where,
          skip,
          take: validatedLimit,
          orderBy: {
            [sortBy]: sortOrder
          }
        }),
        prisma.client.count({
          where
        })
      ]);

      const totalPages = Math.ceil(total / validatedLimit);

      return {
        success: true,
        data: {
          clients,
          pagination: {
            page,
            limit: validatedLimit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      };
    } catch (error) {
      logger.error('Get clients error:', error);
      return {
        success: false,
        message: 'Failed to fetch clients',
        statusCode: 500
      };
    }
  },
  //* Update a client 
  async updateClient(
    id: string,
    organizationId: string,
    data: Prisma.ClientUpdateInput
  ): Promise<ServiceResponse<Client>> {
    try {
      const client = await prisma.client.updateMany({
        where: {
          id,
          organizationId,
          isDeleted: false
        },
        data
      });

      if (client.count === 0) {
        return {
          success: false,
          message: 'Client not found',
          statusCode: 404
        };
      }

      const updatedClient = await prisma.client.findUnique({
        where: { id }
      });

      return {
        success: true,
        data: updatedClient!
      };
    } catch (error) {
      logger.error('Update client error:', error);
      return {
        success: false,
        message: 'Failed to update client',
        statusCode: 500
      };
    }
  },
  //* Soft delete a client
  async deleteClient(id: string, organizationId: string): Promise<ServiceResponse<null>> {
    try {
      const client = await prisma.client.updateMany({
        where: {
          id,
          organizationId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          isActive: false,
          deletedAt: new Date()
        }
      });

      if (client.count === 0) {
        return {
          success: false,
          message: 'Client not found',
          statusCode: 404
        };
      }

      return {
        success: true,
        data: null
      };
    } catch (error) {
      logger.error('Delete client error:', error);
      return {
        success: false,
        message: 'Failed to delete client',
        statusCode: 500
      };
    }
  },
  //* Search clients
  async searchClients(params: {
    query: string;
    organizationId: string;
    page: number;
    limit: number;
    status?: string;
    clientType?: string;
  }): Promise<ServiceResponse<{ clients: Client[]; pagination: any }>> {
    try {
      const {
        query,
        organizationId,
        page,
        limit,
        status,
        clientType,
      } = params;

      const filters: any = {
        organizationId,
        isDeleted: false,
        isActive: true,
        OR: [
          { firstName: { contains: query, mode: 'insensitive' } },
          { lastName: { contains: query, mode: 'insensitive' } },
          { email: { contains: query, mode: 'insensitive' } },
          { phone: { contains: query, mode: 'insensitive' } }
        ]
      };

      if (status) filters.status = status;
      if (clientType) filters.type = clientType;

      const [clients, total] = await Promise.all([
        prisma.client.findMany({
          where: filters,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.client.count({ where: filters })
      ]);

      return {
        success: true,
        data: {
          clients,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
            hasNext: page * limit < total,
            hasPrev: page > 1
          }
        }
      };
    } catch (error) {
      logger.error('Search clients error:', error);
      return {
        success: false,
        message: 'Failed to search clients',
        statusCode: 500
      };
    }
  }
};
