import { Request, Response } from 'express';
import { prismaMock } from '../mocks/prisma';
import { AuthenticatedRequest } from '@crm/shared';
import {
  mockClient,
  mockInactiveClient,
  mockDeletedClient,
  mockClientFromDifferentOrg,
  mockCreateClientRequest,
  mockUpdateClientRequest,
} from '../mocks/testData';
import { mockUser, mockAdmin, mockAgent } from '../mocks/userMocks';
import { ResponseHelper } from '@crm/shared';
import {
  createClient,
  getAllClients,
  getClientById,
  updateClient,
  deleteClient,
  searchClients,
} from '../../controllers/clientController';
import { ClientService } from '../../services/clientService';

// Mock the client service
jest.mock('../../services/clientService');

// Mock ResponseHelper
jest.mock('@crm/shared', () => ({
  ResponseHelper: {
    success: jest.fn(),
    error: jest.fn(),
    notFound: jest.fn(),
    unauthorized: jest.fn(),
    forbidden: jest.fn(),
    badRequest: jest.fn(),
    internalServerError: jest.fn(),
  },
  AuthenticatedRequest: jest.fn(),
  createServiceLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

describe('Client Controller', () => {
  let mockReq: Partial<AuthenticatedRequest>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockReq = {
      user: mockUser,
      body: {},
      params: {},
      query: {},
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    jest.clearAllMocks();

    // Mock prisma user findUnique
    prismaMock.user.findUnique.mockResolvedValue({
      id: mockUser.id,
      organizationId: 'org-123',
    } as any);

    // Mock prisma client findUnique to always return mockClient
    prismaMock.client.findUnique.mockResolvedValue(mockClient);

    // Ensure ClientService methods are always mocks
    ClientService.getClientById = jest.fn();
    ClientService.updateClient = jest.fn();
    ClientService.deleteClient = jest.fn();
    ClientService.createClient = jest.fn();
    ClientService.getClients = jest.fn();
    ClientService.searchClients = jest.fn();
  });

  describe('createClient', () => {
    it('should create a new client successfully', async () => {
      // Arrange
      mockReq.body = mockCreateClientRequest;
      (ClientService.createClient as jest.Mock).mockResolvedValue({
        success: true,
        data: mockClient,
      });

      // Act
      await createClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ClientService.createClient).toHaveBeenCalledWith({
        ...mockCreateClientRequest,
        organizationId: 'org-123',
        ownerId: mockUser.id,
      });
      expect(ResponseHelper.success).toHaveBeenCalledWith(
        mockRes,
        mockClient,
        'Client created successfully',
        201
      );
    });

    it('should handle errors when creating a client', async () => {
      // Arrange
      mockReq.body = mockCreateClientRequest;
      (ClientService.createClient as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Failed to create client',
        statusCode: 500,
      });

      // Act
      await createClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Failed to create client',
      });
    });

    it('should handle unexpected errors when creating a client', async () => {
      // Arrange
      mockReq.body = mockCreateClientRequest;
      (ClientService.createClient as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await createClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ResponseHelper.error).toHaveBeenCalledWith(
        mockRes,
        'Failed to create client',
        500
      );
    });
  });

  describe('getAllClients', () => {
    it('should get all clients successfully', async () => {
      // Arrange
      const mockClients = [mockClient, mockInactiveClient];
      (ClientService.getClients as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          clients: mockClients,
          pagination: {
            total: 2,
            page: 1,
            limit: 20,
          },
        },
      });

      // Act
      await getAllClients(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ClientService.getClients).toHaveBeenCalledWith({
        organizationId: 'org-123',
        page: 1,
        limit: 20,
        status: undefined,
        clientType: undefined,
        source: undefined,
        search: undefined,
      });
      expect(ResponseHelper.success).toHaveBeenCalledWith(
        mockRes,
        {
          clients: mockClients,
          pagination: {
            total: 2,
            page: 1,
            limit: 20,
          },
        }
      );
    });

    it('should handle errors when getting clients', async () => {
      // Arrange
      (ClientService.getClients as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Failed to fetch clients',
        statusCode: 500,
      });

      // Act
      await getAllClients(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Failed to fetch clients',
      });
    });

    it('should handle unexpected errors when getting clients', async () => {
      // Arrange
      (ClientService.getClients as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await getAllClients(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ResponseHelper.error).toHaveBeenCalledWith(
        mockRes,
        'Failed to fetch clients',
        500
      );
    });
  });

  describe('getClientById', () => {
    it('should get a client by ID successfully', async () => {
      // Arrange
      mockReq.params = { id: mockClient.id };
      (ClientService.getClientById as jest.Mock).mockResolvedValue({
        success: true,
        data: mockClient,
      });

      // Act
      await getClientById(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ClientService.getClientById).toHaveBeenCalledWith(mockClient.id, 'org-123');
      expect(ResponseHelper.success).toHaveBeenCalledWith(
        mockRes,
        mockClient
      );
    });

    it('should handle client not found', async () => {
      // Arrange
      mockReq.params = { id: 'non-existent-id' };
      (ClientService.getClientById as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Client not found',
        statusCode: 404,
      });

      // Act
      await getClientById(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Client not found',
      });
    });

    it('should handle unexpected errors when getting client by id', async () => {
      // Arrange
      mockReq.params = { id: 'any-id' };
      (ClientService.getClientById as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await getClientById(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ResponseHelper.error).toHaveBeenCalledWith(
        mockRes,
        'Failed to fetch client',
        500
      );
    });
  });

  describe('updateClient', () => {
    it('should update a client successfully', async () => {
      // Arrange
      mockReq.params = { id: mockClient.id };
      mockReq.body = mockUpdateClientRequest;
      (ClientService.updateClient as jest.Mock).mockResolvedValue({
        success: true,
        data: { ...mockClient, ...mockUpdateClientRequest },
      });

      // Act
      await updateClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ClientService.updateClient).toHaveBeenCalledWith(
        mockClient.id,
        'org-123',
        mockUpdateClientRequest
      );
      expect(ResponseHelper.success).toHaveBeenCalledWith(
        mockRes,
        { ...mockClient, ...mockUpdateClientRequest },
        'Client updated successfully'
      );
    });

    it('should handle client not found during update', async () => {
      // Arrange
      mockReq.params = { id: 'non-existent-id' };
      mockReq.body = mockUpdateClientRequest;
      (ClientService.updateClient as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Client not found',
        statusCode: 404,
      });

      // Act
      await updateClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Client not found',
      });
    });

    it('should handle unexpected errors when updating client', async () => {
      // Arrange
      mockReq.params = { id: 'any-id' };
      mockReq.body = mockUpdateClientRequest;
      (ClientService.updateClient as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await updateClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ResponseHelper.error).toHaveBeenCalledWith(
        mockRes,
        'Failed to update client',
        500
      );
    });
  });

  describe('deleteClient', () => {
    it('should delete a client successfully', async () => {
      // Arrange
      mockReq.params = { id: mockClient.id };
      (ClientService.deleteClient as jest.Mock).mockResolvedValue({
        success: true,
        data: null,
      });

      // Act
      await deleteClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ClientService.deleteClient).toHaveBeenCalledWith(mockClient.id, 'org-123');
      expect(ResponseHelper.success).toHaveBeenCalledWith(
        mockRes,
        null,
        'Client deleted successfully'
      );
    });

    it('should handle client not found during deletion', async () => {
      // Arrange
      mockReq.params = { id: 'non-existent-id' };
      (ClientService.deleteClient as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Client not found',
        statusCode: 404,
      });

      // Act
      await deleteClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Client not found',
      });
    });

    it('should handle unexpected errors when deleting client', async () => {
      // Arrange
      mockReq.params = { id: 'any-id' };
      (ClientService.deleteClient as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await deleteClient(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ResponseHelper.error).toHaveBeenCalledWith(
        mockRes,
        'Failed to delete client',
        500
      );
    });
  });

  describe('searchClients', () => {
    it('should search clients successfully', async () => {
      // Arrange
      mockReq.query = { query: 'John', page: '1', limit: '10' };
      const mockSearchResults = [mockClient];
      (ClientService.searchClients as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          clients: mockSearchResults,
          pagination: {
            total: 1,
            page: 1,
            limit: 10,
          },
        },
      });

      // Act
      await searchClients(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ClientService.searchClients).toHaveBeenCalledWith({
        query: 'John',
        organizationId: 'org-123',
        page: 1,
        limit: 10,
      });
      expect(ResponseHelper.success).toHaveBeenCalledWith(
        mockRes,
        {
          clients: mockSearchResults,
          pagination: {
            total: 1,
            page: 1,
            limit: 10,
          },
        }
      );
    });

    it('should handle errors during client search', async () => {
      // Arrange
      mockReq.query = { query: 'John' };
      (ClientService.searchClients as jest.Mock).mockResolvedValue({
        success: false,
        message: 'Search failed',
        statusCode: 500,
      });

      // Act
      await searchClients(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Search failed',
      });
    });

    it('should handle unexpected errors during client search', async () => {
      // Arrange
      mockReq.query = { query: 'John' };
      (ClientService.searchClients as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act
      await searchClients(mockReq as AuthenticatedRequest, mockRes as Response);

      // Assert
      expect(ResponseHelper.error).toHaveBeenCalledWith(
        mockRes,
        'Failed to search clients',
        500
      );
    });
  });

  afterAll(async () => {
    await prismaMock.$disconnect();
  });
});
