import { prismaMock } from '../mocks/prisma';
import { ClientService } from '../../services/clientService';
import { ClientType, ClientStatus, LeadSource, PropertyType } from '@prisma/client';
import {
  mockClient,
  mockInactiveClient,
  mockDeletedClient,
  mockClientFromDifferentOrg,
  mockCreateClientRequest,
  mockUpdateClientRequest,
} from '../mocks/testData';

// Mock logger
jest.mock('@crm/shared', () => ({
  ...jest.requireActual('@crm/shared'),
  createServiceLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

describe('ClientService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getClients', () => {
    it('should successfully get clients for organization', async () => {
      // Arrange
      prismaMock.client.findMany.mockResolvedValue([mockClient] as any);
      prismaMock.client.count.mockResolvedValue(1);

      // Act
      const result = await ClientService.getClients({
        organizationId: mockClient.organizationId,
        page: 1,
        limit: 20,
      });

      // Assert
      expect(result.success).toBe(true);
      expect(result.data!.clients).toEqual([mockClient]);
      expect(result.data!.pagination).toBeDefined();
      expect(prismaMock.client.findMany).toHaveBeenCalledWith({
        where: {
          organizationId: mockClient.organizationId,
          isDeleted: false,
        },
        skip: 0,
        take: 20,
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should handle search filters', async () => {
      // Arrange
      prismaMock.client.findMany.mockResolvedValue([mockClient] as any);
      prismaMock.client.count.mockResolvedValue(1);

      // Act
      const result = await ClientService.getClients({
        organizationId: mockClient.organizationId,
        search: 'John',
        status: 'ACTIVE',
        clientType: 'BUYER',
        page: 1,
        limit: 20,
      });

      // Assert
      expect(result.success).toBe(true);
      expect(prismaMock.client.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.any(Array),
            status: 'ACTIVE',
            type: 'BUYER',
          }),
        })
      );
    });

    it('should handle database errors', async () => {
      // Arrange
      prismaMock.client.findMany.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await ClientService.getClients({
        organizationId: mockClient.organizationId,
      });

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to fetch clients');
      expect(result.statusCode).toBe(500);
    });
  });

  describe('getClientById', () => {
    it('should successfully get client by ID', async () => {
      // Arrange
      prismaMock.client.findUnique.mockResolvedValue(mockClient as any);

      // Act
      const result = await ClientService.getClientById(
        mockClient.id,
        mockClient.organizationId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockClient);
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: {
          id: mockClient.id,
          organizationId: mockClient.organizationId,
          isDeleted: false,
        },
      });
    });

    it('should fail when client not found', async () => {
      // Arrange
      prismaMock.client.findUnique.mockResolvedValue(null);

      // Act
      const result = await ClientService.getClientById(
        'nonexistent-id',
        mockClient.organizationId
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Client not found');
      expect(result.statusCode).toBe(404);
    });
  });

  describe('createClient', () => {
    it('should successfully create client', async () => {
      // Arrange
      prismaMock.client.create.mockResolvedValue(mockClient as any);

      // Act
      const result = await ClientService.createClient({
        ...mockCreateClientRequest,
        type: ClientType.BUYER,
        status: ClientStatus.ACTIVE,
        source: LeadSource.WEBSITE,
        propertyTypes: [PropertyType.CONDO, PropertyType.TOWNHOUSE],
        organizationId: mockClient.organizationId,
        ownerId: mockClient.ownerId,
      });

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockClient);
      expect(prismaMock.client.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ...mockCreateClientRequest,
          organization: { connect: { id: mockClient.organizationId } },
          owner: { connect: { id: mockClient.ownerId } },
        }),
      });
    });

    it('should handle database errors', async () => {
      // Arrange
      prismaMock.client.create.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await ClientService.createClient({
        ...mockCreateClientRequest,
        type: ClientType.BUYER,
        status: ClientStatus.ACTIVE,
        source: LeadSource.WEBSITE,
        propertyTypes: [PropertyType.CONDO, PropertyType.TOWNHOUSE],
        organizationId: mockClient.organizationId,
        ownerId: mockClient.ownerId,
      });

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to create client');
      expect(result.statusCode).toBe(500);
    });
  });

  describe('updateClient', () => {
    it('should successfully update client', async () => {
      // Arrange
      prismaMock.client.updateMany.mockResolvedValue({ count: 1 });
      prismaMock.client.findUnique.mockResolvedValue({
        ...mockClient,
        ...mockUpdateClientRequest,
      } as any);

      // Act
      const result = await ClientService.updateClient(
        mockClient.id,
        mockClient.organizationId,
        mockUpdateClientRequest
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        ...mockClient,
        ...mockUpdateClientRequest,
      });
    });

    it('should fail when client not found', async () => {
      // Arrange
      prismaMock.client.updateMany.mockResolvedValue({ count: 0 });

      // Act
      const result = await ClientService.updateClient(
        'nonexistent-id',
        mockClient.organizationId,
        mockUpdateClientRequest
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Client not found');
      expect(result.statusCode).toBe(404);
    });
  });

  describe('deleteClient', () => {
    it('should successfully soft delete client', async () => {
      // Arrange
      prismaMock.client.updateMany.mockResolvedValue({ count: 1 });

      // Act
      const result = await ClientService.deleteClient(
        mockClient.id,
        mockClient.organizationId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(null);
      expect(prismaMock.client.updateMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          id: mockClient.id,
          organizationId: mockClient.organizationId,
          isDeleted: false,
        }),
        data: expect.objectContaining({
          isDeleted: true,
          isActive: false,
          deletedAt: expect.any(Date),
        }),
      });
    });

    it('should fail when client not found', async () => {
      // Arrange
      prismaMock.client.updateMany.mockResolvedValue({ count: 0 });

      // Act
      const result = await ClientService.deleteClient(
        'nonexistent-id',
        mockClient.organizationId
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toBe('Client not found');
      expect(result.statusCode).toBe(404);
    });
  });

  describe('searchClients', () => {
    it('should successfully search clients', async () => {
      // Arrange
      prismaMock.client.findMany.mockResolvedValue([mockClient] as any);
      prismaMock.client.count.mockResolvedValue(1);

      // Act
      const result = await ClientService.searchClients({
        query: 'John',
        organizationId: mockClient.organizationId,
        page: 1,
        limit: 20,
      });

      // Assert
      expect(result.success).toBe(true);
      expect(result.data!.clients).toEqual([mockClient]);
      expect(result.data!.pagination).toBeDefined();
      expect(prismaMock.client.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            organizationId: mockClient.organizationId,
            isDeleted: false,
            isActive: true,
            OR: expect.any(Array),
          }),
        })
      );
    });

    it('should handle additional filters', async () => {
      // Arrange
      prismaMock.client.findMany.mockResolvedValue([mockClient] as any);
      prismaMock.client.count.mockResolvedValue(1);

      // Act
      const result = await ClientService.searchClients({
        query: 'John',
        organizationId: mockClient.organizationId,
        page: 1,
        limit: 20,
        status: 'ACTIVE',
        clientType: 'BUYER',
      });

      // Assert
      expect(result.success).toBe(true);
      expect(prismaMock.client.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE',
            type: 'BUYER',
          }),
        })
      );
    });
  });
});
