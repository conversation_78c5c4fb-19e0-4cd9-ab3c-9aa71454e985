import { ClientType, ClientStatus, LeadSource, PropertyType, Prisma } from '@prisma/client';
const { Decimal } = Prisma;

// Test data for clients service tests
export const mockClient = {
  id: 'client-123',
  organizationId: 'org-123',
  firstName: '<PERSON>',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  alternatePhone: null,
  type: ClientType.BUYER,
  status: ClientStatus.ACTIVE,
  source: LeadSource.WEBSITE,
  address: '123 Main St',
  city: 'New York',
  state: 'NY',
  zipCode: '10001',
  country: 'US',
  ownerId: 'user-123',
  budget: new Decimal(500000),
  preferredAreas: ['Downtown', 'Midtown'],
  propertyTypes: [PropertyType.CONDO, PropertyType.TOWNHOUSE],
  notes: 'Interested in 2-bedroom apartments',
  tags: ['VIP', 'First-time buyer'],
  consentGiven: true,
  consentDate: new Date(),
  marketingOptIn: true,
  isActive: true,
  isDeleted: false,
  deletedAt: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockInactiveClient = {
  ...mockClient,
  id: 'client-456',
  status: ClientStatus.INACTIVE,
  isActive: false,
};

export const mockDeletedClient = {
  ...mockClient,
  id: 'client-789',
  isDeleted: true,
  deletedAt: new Date(),
};

export const mockClientFromDifferentOrg = {
  ...mockClient,
  id: 'client-999',
  organizationId: 'org-999',
};

export const mockCreateClientRequest = {
  firstName: 'Jane',
  lastName: 'Smith',
  email: '<EMAIL>',
  phone: '+1987654321',
  type: ClientType.BUYER,
  status: ClientStatus.ACTIVE,
  source: LeadSource.WEBSITE,
  address: '456 Oak St',
  city: 'Los Angeles',
  state: 'CA',
  zipCode: '90001',
  budget: 750000,
  preferredAreas: ['Hollywood', 'Beverly Hills'],
  propertyTypes: [PropertyType.CONDO, PropertyType.TOWNHOUSE],
  notes: 'Looking for luxury condos',
  tags: ['High-end', 'Investor'],
  consentGiven: true,
  marketingOptIn: true,
};

export const mockUpdateClientRequest = {
  firstName: 'Jane',
  lastName: 'Smith-Updated',
  email: '<EMAIL>',
  phone: '+1987654321',
  address: '789 Pine St',
  city: 'San Francisco',
  state: 'CA',
  zipCode: '94101',
  budget: 1000000,
  preferredAreas: ['Financial District', 'Marina'],
  propertyTypes: [PropertyType.CONDO, PropertyType.TOWNHOUSE],
  notes: 'Updated preferences',
  tags: ['VIP', 'Investor'],
};
