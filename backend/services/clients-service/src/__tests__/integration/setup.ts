import { PrismaClient } from '@crm/database';
import { PasswordHelper } from '@crm/shared';

// Set test environment
process.env.NODE_ENV = 'test';

process.env.DATABASE_URL = 'postgresql://crm_user:crm_password@localhost:5434/crm_database';
process.env.JWT_SECRET = 'test_secret';
process.env.REFRESH_TOKEN_SECRET = 'test_refresh_secret';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

export const testDb = prisma;

// Test database connection
export const testDatabaseConnection = async () => {
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
};

// Test data setup
export const setupTestData = async () => {
  // Clean up existing test data
  await cleanupTestData();

  // Create test organizations (needed for clients)
  const testOrganization = await testDb.organization.create({
    data: {
      name: 'Test Organization',
      slug: 'test-org',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Test St',
      website: 'https://test.com',
      timezone: 'UTC',
      currency: 'USD',
    },
  });

  const anotherOrganization = await testDb.organization.create({
    data: {
      name: 'Another Organization',
      slug: 'another-org',
      email: '<EMAIL>',
      phone: '******-9999',
      timezone: 'UTC',
      currency: 'USD',
    },
  });

  // Create test users (needed for client assignments)
  const hashedPassword = await PasswordHelper.hash('password123');

  const agent = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Agent',
      lastName: 'User',
      organizationId: testOrganization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  const anotherAgent = await testDb.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Another',
      lastName: 'Agent',
      organizationId: anotherOrganization.id,
      role: 'AGENT',
      isVerified: true,
    },
  });

  // Create test clients
  const activeClient = await testDb.client.create({
    data: {
      firstName: 'Active',
      lastName: 'Client',
      email: '<EMAIL>',
      phone: '******-0001',
      status: 'ACTIVE',
      organization: {
        connect: { id: testOrganization.id }
      },
      owner: {
        connect: { id: agent.id }
      }
    },
  });

  const inactiveClient = await testDb.client.create({
    data: {
      firstName: 'Inactive',
      lastName: 'Client',
      email: '<EMAIL>',
      phone: '******-0002',
      status: 'INACTIVE',
      organization: {
        connect: { id: testOrganization.id }
      },
      owner: {
        connect: { id: agent.id }
      }
    },
  });

  const anotherOrgClient = await testDb.client.create({
    data: {
      firstName: 'Another',
      lastName: 'Client',
      email: '<EMAIL>',
      phone: '******-0003',
      status: 'ACTIVE',
      organization: {
        connect: { id: anotherOrganization.id }
      },
      owner: {
        connect: { id: anotherAgent.id }
      }
    },
  });

  return {
    testOrganization,
    anotherOrganization,
    agent,
    anotherAgent,
    activeClient,
    inactiveClient,
    anotherOrgClient,
  };
};

export const cleanupTestData = async () => {
  // Delete in correct order due to foreign key constraints
  await testDb.client.deleteMany({});
  await testDb.user.deleteMany({});
  await testDb.organization.deleteMany({
    where: {
      slug: {
        in: ['test-org', 'another-org'],
      },
    },
  });
};

// Setup and teardown hooks
beforeAll(async () => {
  await setupTestData();
});

afterAll(async () => {
  await cleanupTestData();
  await testDb.$disconnect();
});

export default testDb;