import Joi from 'joi';
import {
  ClientType,
  ClientStatus,
  LeadSource,
  PropertyType
} from '@prisma/client';

// Create Client Schema
export const createClientSchema = Joi.object({
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  alternatePhone: Joi.string().optional(),
  type: Joi.string().valid(...Object.values(ClientType)).optional(),
  status: Joi.string().valid(...Object.values(ClientStatus)).optional(),
  source: Joi.string().valid(...Object.values(LeadSource)).optional(),
  address: Joi.string().optional(),
  city: Joi.string().optional(),
  state: Joi.string().optional(),
  zipCode: Joi.string().optional(),
  country: Joi.string().length(2).optional(),
  budget: Joi.number().optional(),
  preferredAreas: Joi.array().items(Joi.string()).optional(),
  propertyTypes: Joi.array().items(Joi.string().valid(...Object.values(PropertyType))).optional(),
  notes: Joi.string().optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  consentGiven: Joi.boolean().optional(),
  consentDate: Joi.date().optional(),
  marketingOptIn: Joi.boolean().optional()
});

// Update Client Schema
export const updateClientSchema = createClientSchema.fork(
  ['firstName', 'lastName'],
  (field) => field.optional()
);

// Query validation (GET /clients)
export const getClientsQuerySchema = Joi.object({
  page: Joi.number().min(1).optional(),
  limit: Joi.number().min(1).max(100).optional(),
  search: Joi.string().optional(),
  status: Joi.string().valid(...Object.values(ClientStatus)).optional(),
  type: Joi.string().valid(...Object.values(ClientType)).optional(),
  source: Joi.string().valid(...Object.values(LeadSource)).optional(),
  tags: Joi.string().optional(),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').optional()
});

// Search validation (GET /clients/search)
export const searchClientsQuerySchema = Joi.object({
  query: Joi.string().required(),
  page: Joi.number().min(1).optional(),
  limit: Joi.number().min(1).max(100).optional(),
  status: Joi.string().valid(...Object.values(ClientStatus)).optional(),
  type: Joi.string().valid(...Object.values(ClientType)).optional()
});