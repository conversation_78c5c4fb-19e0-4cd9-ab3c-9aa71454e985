// Client Types and Enums based on API documentation

export enum ClientType {
  LEAD = "LEAD",
  PROSPECT = "PROSPECT",
  BUYER = "BUYER",
  SELLER = "SELLER",
  TENANT = "TENANT",
  LANDLORD = "LANDLORD",
  INVESTOR = "INVESTOR"
}

export enum ClientStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  CONVERTED = "CONVERTED",
  LOST = "LOST",
  NURTURING = "NURTURING"
}

export enum LeadSource {
  WEBSITE = "WEBSITE",
  REFERRAL = "REFERRAL",
  SOCIAL_MEDIA = "SOCIAL_MEDIA",
  ADVERTISING = "ADVERTISING",
  COLD_CALL = "COLD_CALL",
  EMAIL_CAMPAIGN = "EMAIL_CAMPAIGN",
  WALK_IN = "WALK_IN",
  SIGN_CALL = "SIGN_CALL",
  OPEN_HOUSE = "OPEN_HOUSE",
  OTHER = "OTHER"
}

export enum PropertyType {
  SINGLE_FAMILY = "SINGLE_FAMILY",
  CONDO = "CONDO",
  TOWNHOUSE = "TOWNHOUSE",
  MULTI_FAMILY = "MULTI_FAMILY",
  LAND = "LAND",
  COMMERCIAL = "COMMERCIAL",
  INDUSTRIAL = "INDUSTRIAL",
  RENTAL = "RENTAL",
  OTHER = "OTHER"
}

// Main Client interface based on API documentation
export interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  alternatePhone?: string;
  type: ClientType;
  status: ClientStatus;
  source?: LeadSource;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string; // 2-letter country code
  budget?: number;
  preferredAreas?: string[];
  propertyTypes?: PropertyType[];
  notes?: string;
  tags?: string[];
  consentGiven?: boolean;
  consentDate?: string; // ISO 8601 date
  marketingOptIn?: boolean;
  organizationId: string;
  ownerId: string;
  createdAt: string; // ISO 8601 date
  updatedAt: string; // ISO 8601 date
  isDeleted?: boolean;
}

// Form data interface for creating/updating clients
export interface ClientFormData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  alternatePhone?: string;
  type: ClientType;
  status: ClientStatus;
  source?: LeadSource;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  budget?: number;
  preferredAreas?: string[];
  propertyTypes?: PropertyType[];
  notes?: string;
  tags?: string[];
  consentGiven?: boolean;
  consentDate?: string;
  marketingOptIn?: boolean;
}

// API response interfaces
export interface ClientsResponse {
  success: true;
  data: {
    clients: Client[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

export interface ClientResponse {
  success: true;
  data: Client;
  message?: string;
}

export interface ClientErrorResponse {
  success: false;
  message: string;
  statusCode: number;
}

// Query parameters for fetching clients
export interface ClientsQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ClientStatus;
  type?: ClientType;
  source?: LeadSource;
  tags?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Search parameters
export interface ClientSearchParams {
  query: string;
  page?: number;
  limit?: number;
  status?: ClientStatus;
  type?: ClientType;
}

// Filter options for UI components
export interface ClientFilterOptions {
  statuses: { value: ClientStatus; label: string }[];
  types: { value: ClientType; label: string }[];
  sources: { value: LeadSource; label: string }[];
  propertyTypes: { value: PropertyType; label: string }[];
}

// Utility type for partial client updates
export type ClientUpdateData = Partial<Omit<Client, 'id' | 'organizationId' | 'ownerId' | 'createdAt' | 'updatedAt'>>;

// Hook return types
export interface UseClientsResult {
  clients: Client[];
  pagination: ClientsResponse['data']['pagination'] | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseClientResult {
  client: Client | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Form validation errors
export interface ClientFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  alternatePhone?: string;
  budget?: string;
  zipCode?: string;
  general?: string;
}

// Table column definitions
export interface ClientTableColumn {
  key: keyof Client | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

// Bulk operations
export interface ClientBulkOperation {
  type: 'delete' | 'update_status' | 'update_type' | 'add_tags' | 'remove_tags';
  clientIds: string[];
  data?: {
    status?: ClientStatus;
    type?: ClientType;
    tags?: string[];
  };
}

// Export/Import types
export interface ClientExportOptions {
  format: 'csv' | 'xlsx' | 'json';
  fields: (keyof Client)[];
  filters?: ClientsQueryParams;
}

export interface ClientImportResult {
  success: number;
  failed: number;
  errors: Array<{
    row: number;
    errors: string[];
  }>;
}
