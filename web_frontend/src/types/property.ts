// Property Types and Enums based on API documentation

export enum PropertyType {
  SINGLE_FAMILY = "SINGLE_FAMILY",
  CONDO = "CONDO",
  TOWNHOUSE = "TOWNHOUSE",
  MULTI_FAMILY = "MULTI_FAMILY",
  LAND = "LAND",
  COMMERCIAL = "COMMERCIAL",
  INDUSTRIAL = "INDUSTRIAL",
  RENTAL = "RENTAL",
  OTHER = "OTHER"
}

export enum PropertyStatus {
  AVAILABLE = "AVAILABLE",
  UNDER_CONTRACT = "UNDER_CONTRACT",
  SOLD = "SOLD",
  RENTED = "RENTED",
  OFF_MARKET = "OFF_MARKET",
  COMING_SOON = "COMING_SOON",
  WITHDRAWN = "WITHDRAWN"
}

// Assignee interface for property assignment
export interface PropertyAssignee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

// Main Property interface based on API documentation
export interface Property {
  id: string;
  organizationId: string;
  title: string;
  description?: string;
  propertyType: PropertyType;
  status: PropertyStatus;

  // Location
  address: string;
  city: string;
  state: string;
  zipCode?: string;
  country: string; // 2-letter country code, default: "US"
  latitude?: number;
  longitude?: number;

  // Property Details
  bedrooms?: number;
  bathrooms?: number;
  squareMeters?: number;
  lotSizeMeters?: number;
  yearBuilt?: number;

  // Pricing
  listPrice?: number;
  salePrice?: number;
  rentPrice?: number;
  pricePerSquareMeter?: number;

  // Assignment
  assigneeId?: string;
  assignee?: PropertyAssignee;

  // Features & Media
  features: string[];
  amenities: string[];
  images: string[];
  virtualTourUrl?: string;

  // Listing Details
  mlsNumber?: string;
  listingDate?: string; // ISO 8601 date
  expirationDate?: string; // ISO 8601 date
  daysOnMarket?: number;

  // Status & Timestamps
  isActive: boolean;
  isFeatured: boolean;
  isDeleted: boolean;
  deletedAt?: string; // ISO 8601 date
  createdAt: string; // ISO 8601 date
  updatedAt: string; // ISO 8601 date
}

// Form data interface for creating/updating properties
export interface PropertyFormData {
  title: string;
  description?: string;
  propertyType: PropertyType;
  status?: PropertyStatus;

  // Location
  address: string;
  city: string;
  state: string;
  zipCode?: string;
  country?: string;
  latitude?: number;
  longitude?: number;

  // Property Details
  bedrooms?: number;
  bathrooms?: number;
  squareMeters?: number;
  lotSizeMeters?: number;
  yearBuilt?: number;

  // Pricing
  listPrice?: number;
  salePrice?: number;
  rentPrice?: number;
  pricePerSquareMeter?: number;

  // Assignment
  assigneeId?: string;

  // Features & Media
  features?: string[];
  amenities?: string[];
  images?: string[];
  virtualTourUrl?: string;

  // Listing Details
  mlsNumber?: string;
  listingDate?: string;
  expirationDate?: string;
  daysOnMarket?: number;

  // Status
  isFeatured?: boolean;
}

// API response interfaces
export interface PropertiesResponse {
  success: true;
  data: {
    properties: Property[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message?: string;
}

export interface PropertyResponse {
  success: true;
  data: Property;
  message?: string;
}

export interface PropertyErrorResponse {
  success: false;
  message: string;
  statusCode?: number;
}

// Query parameters for fetching properties
export interface PropertiesQueryParams {
  page?: number;
  limit?: number;
  search?: string;

  // Location filters
  city?: string;
  state?: string;
  zipCode?: string;

  // Property filters
  propertyType?: PropertyType;
  status?: PropertyStatus;
  assigneeId?: string;

  // Size filters
  minSquareMeters?: number;
  maxSquareMeters?: number;
  minLotSize?: number;
  maxLotSize?: number;

  // Bedroom/Bathroom filters
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;

  // Price filters
  minPrice?: number;
  maxPrice?: number;
  priceType?: 'listPrice' | 'salePrice' | 'rentPrice';

  // Date filters
  listedAfter?: string;
  listedBefore?: string;
  updatedAfter?: string;
  updatedBefore?: string;

  // Feature filters
  features?: string | string[];
  amenities?: string | string[];

  // Status filters
  isFeatured?: boolean;
  isActive?: boolean;

  // Sorting
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Search parameters
export interface PropertySearchParams {
  query: string;
  page?: number;
  limit?: number;
  propertyType?: PropertyType;
  status?: PropertyStatus;
  minPrice?: number;
  maxPrice?: number;
  city?: string;
  state?: string;
}

// Filter options for UI components
export interface PropertyFilterOptions {
  propertyTypes: { value: PropertyType; label: string }[];
  statuses: { value: PropertyStatus; label: string }[];
  priceTypes: { value: string; label: string }[];
}

// Utility type for partial property updates
export type PropertyUpdateData = Partial<Omit<Property, 'id' | 'organizationId' | 'createdAt' | 'updatedAt'>>;

// Hook return types
export interface UsePropertiesResult {
  properties: Property[];
  pagination: PropertiesResponse['data']['pagination'] | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UsePropertyResult {
  property: Property | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Form validation errors
export interface PropertyFormErrors {
  title?: string;
  address?: string;
  city?: string;
  state?: string;
  propertyType?: string;
  bedrooms?: string;
  bathrooms?: string;
  squareMeters?: string;
  listPrice?: string;
  salePrice?: string;
  rentPrice?: string;
  yearBuilt?: string;
  latitude?: string;
  longitude?: string;
  general?: string;
}

// Table column definitions
export interface PropertyTableColumn {
  key: keyof Property | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

// Bulk operations
export interface PropertyBulkOperation {
  type: 'delete' | 'update_status' | 'update_type' | 'update_assignee' | 'add_features' | 'remove_features';
  propertyIds: string[];
  data?: {
    status?: PropertyStatus;
    propertyType?: PropertyType;
    assigneeId?: string;
    features?: string[];
  };
}

// Export/Import types
export interface PropertyExportOptions {
  format: 'csv' | 'xlsx' | 'json';
  fields: (keyof Property)[];
  filters?: PropertiesQueryParams;
}

export interface PropertyImportResult {
  success: number;
  failed: number;
  errors: Array<{
    row: number;
    errors: string[];
  }>;
}

// Common feature and amenity options
export interface PropertyFeatureOptions {
  features: string[];
  amenities: string[];
}

// Price range interface for filtering
export interface PriceRange {
  min?: number;
  max?: number;
  type: 'listPrice' | 'salePrice' | 'rentPrice';
}

// Size range interface for filtering
export interface SizeRange {
  minSquareMeters?: number;
  maxSquareMeters?: number;
  minLotSize?: number;
  maxLotSize?: number;
}

// Room range interface for filtering
export interface RoomRange {
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
}
