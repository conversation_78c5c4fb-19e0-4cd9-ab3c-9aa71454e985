export interface Property {
  id: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  country: string;
  listPrice?: number;
  rentPrice?: number;
  squareMeters:number;
  propertyType: string; // ممكن تعمله Enum لو عندك قيم ثابتة
  status: string; // برضو ممكن Enum
  createdAt?: string; // مهم للفرز والترتيب
  updatedAt?: string; // مفيد للعرض أو مقارنة التعديلات
  images?: string[]; // لو بتعرض صور للعقار
  features?: string[]; // مميزات العقار زي "حديقة، جراج، الخ"
  coordinates?: {
    lat: number;
    lng: number;
  }; // لو هتستخدم Map
  assignee?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  organizationId?: string; // لو العقار تابع لشركة أو جهة
  isDeleted?: boolean; // لو بتستخدم Soft Delete
}
