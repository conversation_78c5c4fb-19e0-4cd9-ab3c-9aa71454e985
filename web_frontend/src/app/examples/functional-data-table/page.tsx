"use client"

import { ColumnDef } from "@tanstack/react-table"
import { toast } from "sonner"
import { z } from "zod"
import {
    IconCircleCheckFilled,
    IconDotsVertical,
    IconLoader,
    IconTrendingUp,
} from "@tabler/icons-react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

import {
    FunctionalDataTable,
    TabConfig,
    DrawerConfig,
    createDrawerCell
} from "@/components/functional-data-table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from "@/components/ui/chart"
import {
    DrawerClose,
} from "@/components/ui/drawer"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

// Example 1: Project Tasks Data
const projectTaskSchema = z.object({
    id: z.number(),
    header: z.string(),
    type: z.string(),
    status: z.string(),
    target: z.string(),
    limit: z.string(),
    reviewer: z.string(),
})

const projectTaskData: z.infer<typeof projectTaskSchema>[] = [
    {
        id: 1,
        header: "Executive Summary",
        type: "Executive Summary",
        status: "Done",
        target: "2",
        limit: "4",
        reviewer: "Eddie Lake",
    },
    {
        id: 2,
        header: "Technical Approach",
        type: "Technical Approach",
        status: "In Progress",
        target: "15",
        limit: "20",
        reviewer: "Assign reviewer",
    },
    {
        id: 3,
        header: "Past Performance",
        type: "Narrative",
        status: "Not Started",
        target: "10",
        limit: "15",
        reviewer: "Jamik Tashpulatov",
    },
]

// Chart data and config for drawer examples
const chartData = [
    { month: "January", desktop: 186, mobile: 80 },
    { month: "February", desktop: 305, mobile: 200 },
    { month: "March", desktop: 237, mobile: 120 },
    { month: "April", desktop: 73, mobile: 190 },
    { month: "May", desktop: 209, mobile: 130 },
    { month: "June", desktop: 214, mobile: 140 },
]

const chartConfig = {
    desktop: {
        label: "Desktop",
        color: "var(--primary)",
    },
    mobile: {
        label: "Mobile",
        color: "var(--primary)",
    },
} satisfies ChartConfig

// Project Task Drawer Configuration
const projectTaskDrawerConfig: DrawerConfig<z.infer<typeof projectTaskSchema>> = {
    enabled: true,
    title: (item) => item.header,
    description: () => "Showing total visitors for the last 6 months",
    content: (item) => (
        <>
            <ChartContainer config={chartConfig}>
                <AreaChart
                    accessibilityLayer
                    data={chartData}
                    margin={{
                        left: 0,
                        right: 10,
                    }}
                >
                    <CartesianGrid vertical={false} />
                    <XAxis
                        dataKey="month"
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                        tickFormatter={(value) => value.slice(0, 3)}
                        hide
                    />
                    <ChartTooltip
                        cursor={false}
                        content={<ChartTooltipContent indicator="dot" />}
                    />
                    <Area
                        dataKey="mobile"
                        type="natural"
                        fill="var(--color-mobile)"
                        fillOpacity={0.6}
                        stroke="var(--color-mobile)"
                        stackId="a"
                    />
                    <Area
                        dataKey="desktop"
                        type="natural"
                        fill="var(--color-desktop)"
                        fillOpacity={0.4}
                        stroke="var(--color-desktop)"
                        stackId="a"
                    />
                </AreaChart>
            </ChartContainer>
            <Separator />
            <div className="grid gap-2">
                <div className="flex gap-2 leading-none font-medium">
                    Trending up by 5.2% this month{" "}
                    <IconTrendingUp className="size-4" />
                </div>
                <div className="text-muted-foreground">
                    Showing total visitors for the last 6 months. This is just
                    some random text to test the layout. It spans multiple lines
                    and should wrap around.
                </div>
            </div>
            <Separator />
            <form className="flex flex-col gap-4">
                <div className="flex flex-col gap-3">
                    <Label htmlFor="header">Header</Label>
                    <Input id="header" defaultValue={item.header} />
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col gap-3">
                        <Label htmlFor="type">Type</Label>
                        <Select defaultValue={item.type}>
                            <SelectTrigger id="type" className="w-full">
                                <SelectValue placeholder="Select a type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="Table of Contents">Table of Contents</SelectItem>
                                <SelectItem value="Executive Summary">Executive Summary</SelectItem>
                                <SelectItem value="Technical Approach">Technical Approach</SelectItem>
                                <SelectItem value="Design">Design</SelectItem>
                                <SelectItem value="Capabilities">Capabilities</SelectItem>
                                <SelectItem value="Focus Documents">Focus Documents</SelectItem>
                                <SelectItem value="Narrative">Narrative</SelectItem>
                                <SelectItem value="Cover Page">Cover Page</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="flex flex-col gap-3">
                        <Label htmlFor="status">Status</Label>
                        <Select defaultValue={item.status}>
                            <SelectTrigger id="status" className="w-full">
                                <SelectValue placeholder="Select a status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="Done">Done</SelectItem>
                                <SelectItem value="In Progress">In Progress</SelectItem>
                                <SelectItem value="Not Started">Not Started</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col gap-3">
                        <Label htmlFor="target">Target</Label>
                        <Input id="target" defaultValue={item.target} />
                    </div>
                    <div className="flex flex-col gap-3">
                        <Label htmlFor="limit">Limit</Label>
                        <Input id="limit" defaultValue={item.limit} />
                    </div>
                </div>
                <div className="flex flex-col gap-3">
                    <Label htmlFor="reviewer">Reviewer</Label>
                    <Select defaultValue={item.reviewer}>
                        <SelectTrigger id="reviewer" className="w-full">
                            <SelectValue placeholder="Select a reviewer" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="Eddie Lake">Eddie Lake</SelectItem>
                            <SelectItem value="Jamik Tashpulatov">Jamik Tashpulatov</SelectItem>
                            <SelectItem value="Emily Whalen">Emily Whalen</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </form>
        </>
    ),
    actions: () => (
        <>
            <Button type="submit">Submit</Button>
            <DrawerClose asChild>
                <Button variant="outline">Done</Button>
            </DrawerClose>
        </>
    ),
}

const projectTaskColumns: ColumnDef<z.infer<typeof projectTaskSchema>>[] = [
    {
        accessorKey: "header",
        header: "Header",
        cell: createDrawerCell(
            (item) => item.header,
            projectTaskDrawerConfig
        ),
        enableHiding: false,
    },
    {
        accessorKey: "type",
        header: "Section Type",
        cell: ({ row }) => (
            <div className="w-32">
                <Badge variant="outline" className="text-muted-foreground px-1.5">
                    {row.original.type}
                </Badge>
            </div>
        ),
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => (
            <Badge variant="outline" className="text-muted-foreground px-1.5">
                {row.original.status === "Done" ? (
                    <IconCircleCheckFilled className="fill-green-500 dark:fill-green-400" />
                ) : (
                    <IconLoader />
                )}
                {row.original.status}
            </Badge>
        ),
    },
    {
        accessorKey: "target",
        header: () => <div className="w-full text-right">Target</div>,
        cell: ({ row }) => (
            <form
                onSubmit={(e) => {
                    e.preventDefault()
                    toast.promise(new Promise((resolve) => setTimeout(resolve, 1000)), {
                        loading: `Saving ${row.original.header}`,
                        success: "Done",
                        error: "Error",
                    })
                }}
            >
                <Label htmlFor={`${row.original.id}-target`} className="sr-only">
                    Target
                </Label>
                <Input
                    className="hover:bg-input/30 focus-visible:bg-background dark:hover:bg-input/30 dark:focus-visible:bg-input/30 h-8 w-16 border-transparent bg-transparent text-right shadow-none focus-visible:border dark:bg-transparent"
                    defaultValue={row.original.target}
                    id={`${row.original.id}-target`}
                />
            </form>
        ),
    },
    {
        accessorKey: "limit",
        header: () => <div className="w-full text-right">Limit</div>,
        cell: ({ row }) => (
            <form
                onSubmit={(e) => {
                    e.preventDefault()
                    toast.promise(new Promise((resolve) => setTimeout(resolve, 1000)), {
                        loading: `Saving ${row.original.header}`,
                        success: "Done",
                        error: "Error",
                    })
                }}
            >
                <Label htmlFor={`${row.original.id}-limit`} className="sr-only">
                    Limit
                </Label>
                <Input
                    className="hover:bg-input/30 focus-visible:bg-background dark:hover:bg-input/30 dark:focus-visible:bg-input/30 h-8 w-16 border-transparent bg-transparent text-right shadow-none focus-visible:border dark:bg-transparent"
                    defaultValue={row.original.limit}
                    id={`${row.original.id}-limit`}
                />
            </form>
        ),
    },
    {
        accessorKey: "reviewer",
        header: "Reviewer",
        cell: ({ row }) => {
            const isAssigned = row.original.reviewer !== "Assign reviewer"

            if (isAssigned) {
                return row.original.reviewer
            }

            return (
                <>
                    <Label htmlFor={`${row.original.id}-reviewer`} className="sr-only">
                        Reviewer
                    </Label>
                    <Select>
                        <SelectTrigger
                            className="w-38 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate"
                            size="sm"
                            id={`${row.original.id}-reviewer`}
                        >
                            <SelectValue placeholder="Assign reviewer" />
                        </SelectTrigger>
                        <SelectContent align="end">
                            <SelectItem value="Eddie Lake">Eddie Lake</SelectItem>
                            <SelectItem value="Jamik Tashpulatov">
                                Jamik Tashpulatov
                            </SelectItem>
                        </SelectContent>
                    </Select>
                </>
            )
        },
    },
    {
        id: "actions",
        cell: () => (
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
                        size="icon"
                    >
                        <IconDotsVertical />
                        <span className="sr-only">Open menu</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-32">
                    <DropdownMenuItem>Edit</DropdownMenuItem>
                    <DropdownMenuItem>Make a copy</DropdownMenuItem>
                    <DropdownMenuItem>Favorite</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem variant="destructive">Delete</DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        ),
    },
]

const projectTabs: TabConfig[] = [
    { value: "outline", label: "Outline" },
    { value: "past-performance", label: "Past Performance", badge: 3 },
    { value: "key-personnel", label: "Key Personnel", badge: 2 },
    { value: "focus-documents", label: "Focus Documents" },
]

// Example 2: Users Data
const userSchema = z.object({
    id: z.number(),
    name: z.string(),
    email: z.string(),
    role: z.string(),
    status: z.string(),
    joinDate: z.string(),
})

const userData: z.infer<typeof userSchema>[] = [
    {
        id: 1,
        name: "John Doe",
        email: "<EMAIL>",
        role: "Admin",
        status: "Active",
        joinDate: "2023-01-15",
    },
    {
        id: 2,
        name: "Jane Smith",
        email: "<EMAIL>",
        role: "Editor",
        status: "Active",
        joinDate: "2023-02-20",
    },
    {
        id: 3,
        name: "Bob Johnson",
        email: "<EMAIL>",
        role: "Viewer",
        status: "Inactive",
        joinDate: "2023-03-10",
    },
]

// User Drawer Configuration
const userDrawerConfig: DrawerConfig<z.infer<typeof userSchema>> = {
    enabled: true,
    title: (item) => `User: ${item.name}`,
    description: () => "User profile information",
    content: (item) => (
        <form className="flex flex-col gap-4">
            <div className="flex flex-col gap-3">
                <Label htmlFor="user-name">Name</Label>
                <Input id="user-name" defaultValue={item.name} />
            </div>
            <div className="flex flex-col gap-3">
                <Label htmlFor="user-email">Email</Label>
                <Input id="user-email" defaultValue={item.email} />
            </div>
            <div className="grid grid-cols-2 gap-4">
                <div className="flex flex-col gap-3">
                    <Label htmlFor="user-role">Role</Label>
                    <Select defaultValue={item.role}>
                        <SelectTrigger id="user-role">
                            <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="Admin">Admin</SelectItem>
                            <SelectItem value="Editor">Editor</SelectItem>
                            <SelectItem value="Viewer">Viewer</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex flex-col gap-3">
                    <Label htmlFor="user-status">Status</Label>
                    <Select defaultValue={item.status}>
                        <SelectTrigger id="user-status">
                            <SelectValue placeholder="Select a status" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="Active">Active</SelectItem>
                            <SelectItem value="Inactive">Inactive</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>
            <div className="flex flex-col gap-3">
                <Label htmlFor="user-join-date">Join Date</Label>
                <Input id="user-join-date" defaultValue={item.joinDate} type="date" />
            </div>
        </form>
    ),
}

const userColumns: ColumnDef<z.infer<typeof userSchema>>[] = [
    {
        accessorKey: "name",
        header: "Name",
        cell: createDrawerCell(
            (item) => item.name,
            userDrawerConfig
        ),
        enableHiding: false,
    },
    {
        accessorKey: "email",
        header: "Email",
    },
    {
        accessorKey: "role",
        header: "Role",
        cell: ({ row }) => (
            <Badge variant="outline" className="text-muted-foreground px-1.5">
                {row.original.role}
            </Badge>
        ),
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => (
            <Badge
                variant={row.original.status === "Active" ? "default" : "secondary"}
                className="text-muted-foreground px-1.5"
            >
                {row.original.status}
            </Badge>
        ),
    },
    {
        accessorKey: "joinDate",
        header: "Join Date",
    },
    {
        id: "actions",
        cell: () => (
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
                        size="icon"
                    >
                        <IconDotsVertical />
                        <span className="sr-only">Open menu</span>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-32">
                    <DropdownMenuItem>View Profile</DropdownMenuItem>
                    <DropdownMenuItem>Edit User</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem variant="destructive">Delete</DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        ),
    },
]

// Example 3: Products Data
const productSchema = z.object({
    id: z.number(),
    name: z.string(),
    category: z.string(),
    price: z.number(),
    stock: z.number(),
    featured: z.boolean(),
})

const productData: z.infer<typeof productSchema>[] = [
    {
        id: 1,
        name: "Wireless Headphones",
        category: "Electronics",
        price: 99.99,
        stock: 50,
        featured: true,
    },
    {
        id: 2,
        name: "Coffee Mug",
        category: "Kitchen",
        price: 12.99,
        stock: 100,
        featured: false,
    },
    {
        id: 3,
        name: "Desk Lamp",
        category: "Office",
        price: 45.00,
        stock: 25,
        featured: true,
    },
]

const productColumns: ColumnDef<z.infer<typeof productSchema>>[] = [
    {
        accessorKey: "name",
        header: "Product Name",
        enableHiding: false,
    },
    {
        accessorKey: "category",
        header: "Category",
        cell: ({ row }) => (
            <Badge variant="outline" className="text-muted-foreground px-1.5">
                {row.original.category}
            </Badge>
        ),
    },
    {
        accessorKey: "price",
        header: () => <div className="text-right">Price</div>,
        cell: ({ row }) => (
            <div className="text-right font-medium">
                ${row.original.price.toFixed(2)}
            </div>
        ),
    },
    {
        accessorKey: "stock",
        header: () => <div className="text-right">Stock</div>,
        cell: ({ row }) => (
            <div className="text-right">
                {row.original.stock}
            </div>
        ),
    },
    {
        accessorKey: "featured",
        header: "Featured",
        cell: ({ row }) => (
            <Badge
                variant={row.original.featured ? "default" : "secondary"}
                className="text-muted-foreground px-1.5"
            >
                {row.original.featured ? "Yes" : "No"}
            </Badge>
        ),
    },
]

export default function FunctionalDataTableExamples() {
    return (
        <div className="container mx-auto py-8 space-y-12">
            <div className="space-y-4">
                <h1 className="text-3xl font-bold">Functional Data Table Examples</h1>
                <p className="text-muted-foreground">
                    This page demonstrates different configurations of the FunctionalDataTable component.
                </p>
            </div>

            {/* Example 1: Full Featured Table with Tabs and Drawer */}
            <div className="space-y-4">
                <div className="space-y-2">
                    <h2 className="text-2xl font-semibold">Example 1: Project Tasks with Tabs and Drawer</h2>
                    <p className="text-muted-foreground">
                        A fully featured table with drag & drop, row selection, tabs, and custom drawer. Click on any header to open detailed view with charts and forms.
                    </p>
                </div>
                <FunctionalDataTable
                    data={projectTaskData}
                    columns={projectTaskColumns}
                    schema={projectTaskSchema}
                    getRowId={(row) => row.id}
                    tabs={projectTabs}
                    defaultTab="outline"
                    addButtonLabel="Add Section"
                    columnsButtonLabel="Customize Columns"
                    onAddClick={() => toast.success("Add Section clicked!")}
                />
            </div>

            {/* Example 2: Users with Drawer */}
            <div className="space-y-4">
                <div className="space-y-2">
                    <h2 className="text-2xl font-semibold">Example 2: Users with Drawer</h2>
                    <p className="text-muted-foreground">
                        Click on a user name to open a detailed drawer with editable information.
                    </p>
                </div>
                <FunctionalDataTable
                    data={userData}
                    columns={userColumns}
                    schema={userSchema}
                    getRowId={(row) => row.id}
                    enableDragAndDrop={false}
                    title="Users"
                    addButtonLabel="Add User"
                    onAddClick={() => toast.success("Add User clicked!")}
                    initialPageSize={5}
                />
            </div>

            {/* Example 3: Products Table without Row Selection */}
            <div className="space-y-4">
                <div className="space-y-2">
                    <h2 className="text-2xl font-semibold">Example 3: Products Catalog</h2>
                    <p className="text-muted-foreground">
                        A products table with drag & drop enabled but without row selection.
                    </p>
                </div>
                <FunctionalDataTable
                    data={productData}
                    columns={productColumns}
                    schema={productSchema}
                    getRowId={(row) => row.id}
                    enableRowSelection={false}
                    title="Products"
                    addButtonLabel="Add Product"
                    onAddClick={() => toast.success("Add Product clicked!")}
                    emptyStateMessage="No products found."
                    pageSizeOptions={[5, 10, 15]}
                />
            </div>

            {/* Example 4: Minimal Configuration */}
            <div className="space-y-4">
                <div className="space-y-2">
                    <h2 className="text-2xl font-semibold">Example 4: Minimal Configuration</h2>
                    <p className="text-muted-foreground">
                        The most basic configuration with minimal features enabled.
                    </p>
                </div>
                <FunctionalDataTable
                    data={userData}
                    columns={[
                        {
                            accessorKey: "name",
                            header: "Name",
                        },
                        {
                            accessorKey: "email",
                            header: "Email",
                        },
                        {
                            accessorKey: "role",
                            header: "Role",
                        },
                    ]}
                    schema={userSchema}
                    getRowId={(row) => row.id}
                    enableDragAndDrop={false}
                    enableRowSelection={false}
                    enableColumnVisibility={false}
                    enablePagination={false}
                />
            </div>

            {/* Documentation Section */}
            <div className="space-y-4">
                <div className="space-y-2">
                    <h2 className="text-2xl font-semibold">How to Use Drawer Functionality</h2>
                    <p className="text-muted-foreground">
                        The FunctionalDataTable supports customizable drawer/dialog functionality for displaying detailed information.
                    </p>
                </div>
                <div className="rounded-lg border p-6 space-y-4">
                    <h3 className="text-lg font-semibold">Basic Usage</h3>
                    <pre className="bg-muted p-4 rounded text-sm overflow-x-auto">
                        {`// 1. Create a drawer configuration
const drawerConfig: DrawerConfig<YourDataType> = {
  enabled: true,
  title: (item) => item.name,
  description: (item) => "Details for " + item.name,
  content: (item) => (
    <div>Your custom content here</div>
  ),
  actions: (item) => (
    <>
      <Button>Save</Button>
      <DrawerClose asChild>
        <Button variant="outline">Cancel</Button>
      </DrawerClose>
    </>
  ),
}

// 2. Use createDrawerCell in your column definition
const columns = [
  {
    accessorKey: "name",
    header: "Name",
    cell: createDrawerCell(
      (item) => item.name, // What to display as trigger
      drawerConfig
    ),
  },
  // ... other columns
]

// 3. Use the table normally
<FunctionalDataTable
  data={data}
  columns={columns}
  schema={schema}
  getRowId={(row) => row.id}
/>`}
                    </pre>
                    <div className="space-y-2">
                        <h4 className="font-medium">Drawer Configuration Options:</h4>
                        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                            <li><code>enabled</code>: Boolean to enable/disable drawer functionality</li>
                            <li><code>title</code>: Function that returns the drawer title</li>
                            <li><code>description</code>: Function that returns the drawer description</li>
                            <li><code>content</code>: Function that returns the main drawer content (JSX)</li>
                            <li><code>actions</code>: Function that returns custom action buttons</li>
                            <li><code>triggerRender</code>: Optional custom trigger element renderer</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    )
} 