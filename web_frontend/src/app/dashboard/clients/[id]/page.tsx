"use client"

import { useEffect, useState } from "react"
import { usePara<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"

type Client = {
    id: string
    firstName: string
    lastName: string
    email: string
    phone: string
    status: string
    type: string
    organizationName: string
}

export default function ViewClientPage() {
    const { id } = useParams()
    const [client, setClient] = useState<Client | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const fetchClient = async () => {
            setLoading(true)
            try {
                const token = localStorage.getItem("token")
                const res = await fetch(`http://localhost:3000/api/clients/${id}`, {
                    headers: {
                        Authorization: `Bear<PERSON> ${token}`,
                    },
                })
                if (!res.ok) throw new Error("Failed to fetch client")
                const data = await res.json()
                console.log("Client data:", data.data)
                setClient(data.data)
            } catch (err: any) {
                setError(err.message)
            } finally {
                setLoading(false)
            }
        }

        if (id) fetchClient()
    }, [id])

    if (loading) return <div className="p-4">Loading client data...</div>
    if (error) return <div className="p-4 text-red-500">{error}</div>
    if (!client) return <div className="p-4 text-gray-500">No client found.</div>

    return (
        <div className="p-4 max-w-2xl mx-auto">
            <Card className="shadow-lg border-0 rounded-xl">
                <CardHeader>
                    <CardTitle className="text-xl font-bold text-indigo-700">
                        Client Details
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label>First Name</Label>
                            <Input value={client.firstName} readOnly />
                        </div>
                        <div>
                            <Label>Last Name</Label>
                            <Input value={client.lastName} readOnly />
                        </div>
                        <div>
                            <Label>Email</Label>
                            <Input value={client.email} readOnly />
                        </div>
                        <div>
                            <Label>Phone</Label>
                            <Input value={client.phone} readOnly />
                        </div>
                        <div>
                            <Label>Status</Label>
                            <Badge variant="outline" className="px-3 py-1">{client.status}</Badge>
                        </div>
                        <div>
                            <Label>Type</Label>
                            <Badge variant="outline" className="px-3 py-1">{client.type}</Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
