'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { ClientDetailView } from '@/components/clients/client-detail-view';
import { ClientFormDialog } from '@/components/clients/client-form-dialog';
import { useClient } from '@/hooks/use-clients';


export default function ClientDetailPage() {
  const params = useParams();
  const clientId = params.id as string;

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { client, loading, error, refetch } = useClient(clientId);

  const handleEdit = () => {
    setIsEditDialogOpen(true);
  };

  const handleEditSuccess = () => {
    refetch();
    setIsEditDialogOpen(false);
  };

  const handleDeleted = () => {
    // Navigation back to clients list is handled in ClientDetailView
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading client details...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-destructive mb-4">Error loading client: {error}</p>
              <p className="text-muted-foreground">
                The client may not exist or you may not have permission to view it.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-muted-foreground">Client not found.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <ClientDetailView
        client={client}
        onEdit={handleEdit}
        onDeleted={handleDeleted}
      />

      <ClientFormDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        client={client}
        onSuccess={handleEditSuccess}
      />
    </>
  );
}
