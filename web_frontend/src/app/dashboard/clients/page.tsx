'use client';

import { useState, useMemo } from 'react';
import { Plus, Search, Download, Upload } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';


import { ClientsDataTable } from '@/components/clients/clients-data-table';
import { ClientFormDialog } from '@/components/clients/client-form-dialog';
import { EmptyState } from '@/components/clients/empty-state';
import { LoadingState } from '@/components/clients/loading-state';
import { AdvancedSearchDialog } from '@/components/clients/advanced-search-dialog';
import { useClients, useDebouncedClientSearch } from '@/hooks/use-clients';
import {
  Client,
  ClientsQueryParams,
  ClientStatus,
  ClientType,
  LeadSource,
} from '@/types/client';

export default function ClientsPage() {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [queryParams, setQueryParams] = useState<ClientsQueryParams>({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { query, debouncedQuery, setQuery } = useDebouncedClientSearch();

  // Combine query params with debounced search
  const finalQueryParams = useMemo(() => ({
    ...queryParams,
    search: debouncedQuery || undefined,
  }), [queryParams, debouncedQuery]);

  const { clients, pagination, loading, error, refetch } = useClients(finalQueryParams);

  const handleAddClient = () => {
    setSelectedClient(null);
    setIsFormDialogOpen(true);
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setIsFormDialogOpen(true);
  };

  const handleFormSuccess = () => {
    refetch();
    setIsFormDialogOpen(false);
    setSelectedClient(null);
  };

  const handleStatusFilter = (status: string) => {
    setQueryParams(prev => ({
      ...prev,
      status: status === 'all' ? undefined : (status as ClientStatus),
      page: 1,
    }));
  };

  const handleTypeFilter = (type: string) => {
    setQueryParams(prev => ({
      ...prev,
      type: type === 'all' ? undefined : (type as ClientType),
      page: 1,
    }));
  };

  const handleSourceFilter = (source: string) => {
    setQueryParams(prev => ({
      ...prev,
      source: source === 'all' ? undefined : (source as LeadSource),
      page: 1,
    }));
  };

  const handleAdvancedSearch = (params: ClientsQueryParams) => {
    setQueryParams(params);
  };

  const handleClearSearch = () => {
    setQuery('');
    // No need to update queryParams here since finalQueryParams will handle it
  };

  const handleClearFilters = () => {
    setQuery('');
    setQueryParams({
      page: 1,
      limit: 20,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  };

  const getStatusCounts = () => {
    const counts = {
      total: clients.length,
      active: clients.filter(c => c.status === ClientStatus.ACTIVE).length,
      converted: clients.filter(c => c.status === ClientStatus.CONVERTED).length,
      nurturing: clients.filter(c => c.status === ClientStatus.NURTURING).length,
      inactive: clients.filter(c => c.status === ClientStatus.INACTIVE).length,
      lost: clients.filter(c => c.status === ClientStatus.LOST).length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-destructive mb-4">Error loading clients: {error}</p>
              <Button onClick={refetch}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Clients</h1>
          <p className="text-muted-foreground">
            Manage your client relationships and track their journey
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleAddClient}>
            <Plus className="h-4 w-4 mr-2" />
            Add Client
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{pagination?.total || statusCounts.total}</p>
              <p className="text-sm text-muted-foreground">Total Clients</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{statusCounts.active}</p>
              <p className="text-sm text-muted-foreground">Active</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{statusCounts.converted}</p>
              <p className="text-sm text-muted-foreground">Converted</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{statusCounts.nurturing}</p>
              <p className="text-sm text-muted-foreground">Nurturing</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-600">{statusCounts.inactive}</p>
              <p className="text-sm text-muted-foreground">Inactive</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{statusCounts.lost}</p>
              <p className="text-sm text-muted-foreground">Lost</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Search & Filter</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search clients by name, email, or phone..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex space-x-2">
              <Select
                value={queryParams.status || 'all'}
                onValueChange={handleStatusFilter}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  {Object.values(ClientStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={queryParams.type || 'all'}
                onValueChange={handleTypeFilter}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.values(ClientType).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={queryParams.source || 'all'}
                onValueChange={handleSourceFilter}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  {Object.values(LeadSource).map((source) => (
                    <SelectItem key={source} value={source}>
                      {source.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <AdvancedSearchDialog
                onSearch={handleAdvancedSearch}
                currentParams={queryParams}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Clients Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Clients List</CardTitle>
            {pagination && (
              <Badge variant="outline">
                {pagination.total} total clients
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <LoadingState type="table" />
          ) : clients.length === 0 ? (
            debouncedQuery ? (
              <EmptyState
                type="no-search-results"
                onClearSearch={handleClearSearch}
              />
            ) : (queryParams.status || queryParams.type || queryParams.source) ? (
              <EmptyState
                type="no-filtered-results"
                onClearFilters={handleClearFilters}
              />
            ) : (
              <EmptyState
                type="no-clients"
                onAddClient={handleAddClient}
              />
            )
          ) : (
            <ClientsDataTable
              data={clients}
              onEdit={handleEditClient}
              onRefresh={refetch}
            />
          )}
        </CardContent>
      </Card>

      {/* Client Form Dialog */}
      <ClientFormDialog
        open={isFormDialogOpen}
        onOpenChange={setIsFormDialogOpen}
        client={selectedClient}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
}
