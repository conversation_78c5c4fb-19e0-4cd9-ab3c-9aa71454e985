"use client"

import { useEffect, useState } from "react"
import { fetchClients, type Client } from "@/lib/fetchClients"
import { But<PERSON> } from "@/components/ui/button"
import { DataTable } from "@/components/data-table"
import { ClientFormDialog } from "@/components/client-form-dialog"
export default function ClientsPage() {
  const [data, setData] = useState<Client[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)

  useEffect(() => {
    const loadClients = async () => {
      try {
        const clients = await fetchClients()
        setData(clients)
      } catch (err) {
        console.error("Error loading clients:", err)
        setError("Failed to load data")
      } finally {
        setIsLoading(false)
      }
    }

    loadClients()
  }, [])

  const handleAddClient = () => {
    setEditingClient(null)
    setIsDialogOpen(true)
  }

  const handleEditClient = (client: Client) => {
    setEditingClient(client)
    setIsDialogOpen(true)
  }

  const handleDialogSuccess = () => {
    // Reload clients data
    const loadClients = async () => {
      try {
        const clients = await fetchClients()
        setData(clients)
      } catch (err) {
        console.error("Error loading clients:", err)
        setError("Failed to load data")
      }
    }
    loadClients()
  }

  if (isLoading) return <div className="p-4">Loading data...</div>
  if (error) return <div className="p-4 text-red-500">{error}</div>

  return (
    <div className="p-6 bg-white shadow rounded-xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-indigo-800">Clients List</h1>
        <Button
          className="bg-indigo-600 hover:bg-indigo-700"
          onClick={handleAddClient}
        >
          ➕ Add Client
        </Button>
      </div>
      <DataTable
        data={data.map((client) => ({
          ...client,
          email: client.email ?? "",
          phone: client.phone ?? "",
          status: client.status ?? "",
          type: client.type ?? "",
        }))}
        onEdit={handleEditClient}
      />

      <ClientFormDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        client={editingClient}
        onSuccess={handleDialogSuccess}
      />
    </div>
  )
}
