
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { IconPlus, IconSearch } from "@tabler/icons-react"
import { Input } from "@/components/ui/input"

export default function OpportunitiesPage() {
    return (

        <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
                <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                    <div className="px-4 lg:px-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">Opportunities</h1>
                                <p className="text-muted-foreground">
                                    Manage sales opportunities and deals
                                </p>
                            </div>
                            <Button>
                                <IconPlus className="mr-2 h-4 w-4" />
                                Add Opportunity
                            </Button>
                        </div>
                    </div>

                    <div className="px-4 lg:px-6">
                        <div className="flex items-center space-x-2">
                            <div className="relative flex-1 max-w-sm">
                                <IconSearch className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input placeholder="Search opportunities..." className="pl-8" />
                            </div>
                        </div>
                    </div>

                    <div className="px-4 lg:px-6">
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Enterprise Software Deal</CardTitle>
                                            <CardDescription>Acme Corporation</CardDescription>
                                        </div>
                                        <Badge variant="default">Proposal</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="text-muted-foreground">Value:</span>
                                            <span className="font-medium">$125,000</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-muted-foreground">Close Date:</span>
                                            <span>Dec 15, 2024</span>
                                        </div>
                                        <div className="space-y-1">
                                            <div className="flex justify-between text-sm">
                                                <span className="text-muted-foreground">Progress:</span>
                                                <span>75%</span>
                                            </div>
                                            <Progress value={75} className="h-2" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Marketing Automation</CardTitle>
                                            <CardDescription>Global Solutions Inc</CardDescription>
                                        </div>
                                        <Badge variant="secondary">Negotiation</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="text-muted-foreground">Value:</span>
                                            <span className="font-medium">$75,000</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-muted-foreground">Close Date:</span>
                                            <span>Jan 30, 2025</span>
                                        </div>
                                        <div className="space-y-1">
                                            <div className="flex justify-between text-sm">
                                                <span className="text-muted-foreground">Progress:</span>
                                                <span>60%</span>
                                            </div>
                                            <Progress value={60} className="h-2" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Custom Development</CardTitle>
                                            <CardDescription>Tech Innovations LLC</CardDescription>
                                        </div>
                                        <Badge variant="outline">Discovery</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span className="text-muted-foreground">Value:</span>
                                            <span className="font-medium">$200,000</span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span className="text-muted-foreground">Close Date:</span>
                                            <span>Mar 15, 2025</span>
                                        </div>
                                        <div className="space-y-1">
                                            <div className="flex justify-between text-sm">
                                                <span className="text-muted-foreground">Progress:</span>
                                                <span>25%</span>
                                            </div>
                                            <Progress value={25} className="h-2" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    )
} 