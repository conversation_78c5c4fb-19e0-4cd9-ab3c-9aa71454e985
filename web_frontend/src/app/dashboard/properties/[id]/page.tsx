'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { PropertyDetailView } from '@/components/properties/property-detail-view';
import { PropertyFormDialog } from '@/components/properties/property-form-dialog';
import { useProperty } from '@/hooks/use-properties';

export default function PropertyDetailPage() {
  const params = useParams();
  const propertyId = params.id as string;
  
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { property, loading, error, refetch } = useProperty(propertyId);

  const handleEdit = () => {
    setIsEditDialogOpen(true);
  };

  const handleEditSuccess = () => {
    refetch();
    setIsEditDialogOpen(false);
  };

  const handleDeleted = () => {
    // Navigation back to properties list is handled in PropertyDetailView
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading property details...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-destructive mb-4">Error loading property: {error}</p>
              <p className="text-muted-foreground">
                The property may not exist or you may not have permission to view it.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-muted-foreground">Property not found.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <PropertyDetailView
        property={property}
        onEdit={handleEdit}
        onDeleted={handleDeleted}
      />
      
      <PropertyFormDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        property={property}
        onSuccess={handleEditSuccess}
      />
    </>
  );
}
