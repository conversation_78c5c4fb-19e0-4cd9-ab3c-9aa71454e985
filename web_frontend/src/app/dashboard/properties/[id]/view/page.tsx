
'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'

type Property = {
  id: string
  title: string
  description?: string
  propertyType: string
  status?: string
  address: string
  city: string
  state: string
  zipCode?: string
  country?: string
  listPrice?: number
  bedrooms?: number
  bathrooms?: number
  squareFeet?: number
  yearBuilt?: number
  features?: string[]
  images?: string[]
  createdAt: string
}

export default function ViewPropertyPage() {
  const { id } = useParams()
  const [property, setProperty] = useState<Property | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchProperty = async () => {
      try {
        const token = localStorage.getItem('token')
        console.log(token)
        const res = await fetch(`http://localhost:3000/api/properties/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        const data = await res.json()
        setProperty(data.data)
      } catch (error) {
        console.error('فشل تحميل العقار', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProperty()
  }, [id])

  if (loading) {
    return (
      <div className="p-6">
        <Skeleton className="h-8 w-1/3 mb-4" />
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-full" />
      </div>
    )
  }

  if (!property) {
    return <div className="p-6 text-red-500">لم يتم العثور على العقار</div>
  }

  return (
    <div className="p-6">
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="space-y-4 p-6">
          <h2 className="text-2xl font-bold text-primary">{property.title}</h2>
          <p className="text-gray-600">{property.description || 'لا يوجد وصف.'}</p>
          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Info label="النوع" value={property.propertyType} />
            <Info label="الحالة" value={property.status || 'غير محددة'} />
            <Info label="العنوان" value={property.address} />
            <Info label="المدينة" value={property.city} />
            <Info label="المحافظة" value={property.state} />
            <Info label="الدولة" value={property.country || 'غير محددة'} />
            <Info label="السعر" value={property.listPrice ? `${property.listPrice} ج.م` : 'غير محدد'} />
            <Info label="عدد الغرف" value={property.bedrooms?.toString() || 'غير محدد'} />
            <Info label="عدد الحمامات" value={property.bathrooms?.toString() || 'غير محدد'} />
            <Info label="المساحة" value={property.squareFeet ? `${property.squareFeet} م²` : 'غير محددة'} />
            <Info label="سنة البناء" value={property.yearBuilt?.toString() || 'غير محددة'} />
            <Info label="الرمز البريدي" value={property.zipCode || 'غير متوفر'} />
            <Info label="تاريخ الإضافة" value={new Date(property.createdAt).toLocaleDateString()} />
          </div>

          {property.features && property.features.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold mb-2">المميزات:</h3>
                <ul className="list-disc list-inside space-y-1">
                  {property.features.map((feature, idx) => (
                    <li key={idx}>{feature}</li>
                  ))}
                </ul>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function Info({ label, value }: { label: string; value: string }) {
  return (
    <div>
      <p className="text-sm text-muted-foreground">{label}</p>
      <p className="text-base font-medium">{value}</p>
    </div>
  )
}
