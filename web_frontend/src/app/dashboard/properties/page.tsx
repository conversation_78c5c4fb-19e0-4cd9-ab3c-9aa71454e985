'use client';

import { useState, useMemo } from 'react';
import { Plus, Search, Download, Upload } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

import { PropertiesDataTable } from '@/components/properties/properties-data-table';
import { PropertyFormDialog } from '@/components/properties/property-form-dialog';
import { AdvancedSearchDialog } from '@/components/properties/advanced-search-dialog';
import { EmptyState } from '@/components/clients/empty-state';
import { LoadingState } from '@/components/clients/loading-state';
import { useProperties, useDebouncedPropertySearch } from '@/hooks/use-properties';
import {
  Property,
  PropertiesQueryParams,
  PropertyStatus,
  PropertyType,
} from '@/types/property';

export default function PropertiesPage() {
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [queryParams, setQueryParams] = useState<PropertiesQueryParams>({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { query, debouncedQuery, setQuery } = useDebouncedPropertySearch();

  // Combine query params with debounced search
  const finalQueryParams = useMemo(() => ({
    ...queryParams,
    search: debouncedQuery || undefined,
  }), [queryParams, debouncedQuery]);

  const { properties, pagination, loading, error, refetch } = useProperties(finalQueryParams);

  const handleAddProperty = () => {
    setSelectedProperty(null);
    setIsFormDialogOpen(true);
  };

  const handleEditProperty = (property: Property) => {
    setSelectedProperty(property);
    setIsFormDialogOpen(true);
  };

  const handleFormSuccess = () => {
    refetch();
    setIsFormDialogOpen(false);
    setSelectedProperty(null);
  };

  const handleStatusFilter = (status: string) => {
    setQueryParams(prev => ({
      ...prev,
      status: status === 'all' ? undefined : (status as PropertyStatus),
      page: 1,
    }));
  };

  const handleTypeFilter = (type: string) => {
    setQueryParams(prev => ({
      ...prev,
      propertyType: type === 'all' ? undefined : (type as PropertyType),
      page: 1,
    }));
  };

  const handleClearSearch = () => {
    setQuery('');
    // No need to update queryParams here since finalQueryParams will handle it
  };

  const handleClearFilters = () => {
    setQuery('');
    setQueryParams({
      page: 1,
      limit: 20,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  };

  const handleAdvancedSearch = (params: PropertiesQueryParams) => {
    setQueryParams(params);
  };

  const getStatusCounts = () => {
    const counts = {
      total: properties.length,
      available: properties.filter(p => p.status === PropertyStatus.AVAILABLE).length,
      underContract: properties.filter(p => p.status === PropertyStatus.UNDER_CONTRACT).length,
      sold: properties.filter(p => p.status === PropertyStatus.SOLD).length,
      rented: properties.filter(p => p.status === PropertyStatus.RENTED).length,
      offMarket: properties.filter(p => p.status === PropertyStatus.OFF_MARKET).length,
      featured: properties.filter(p => p.isFeatured).length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-destructive mb-4">Error loading properties: {error}</p>
              <Button onClick={refetch}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Properties</h1>
          <p className="text-muted-foreground">
            Manage your property listings and track their status
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleAddProperty}>
            <Plus className="h-4 w-4 mr-2" />
            Add Property
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{pagination?.total || statusCounts.total}</p>
              <p className="text-sm text-muted-foreground">Total Properties</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{statusCounts.available}</p>
              <p className="text-sm text-muted-foreground">Available</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{statusCounts.underContract}</p>
              <p className="text-sm text-muted-foreground">Under Contract</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{statusCounts.sold}</p>
              <p className="text-sm text-muted-foreground">Sold</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{statusCounts.rented}</p>
              <p className="text-sm text-muted-foreground">Rented</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-600">{statusCounts.offMarket}</p>
              <p className="text-sm text-muted-foreground">Off Market</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">{statusCounts.featured}</p>
              <p className="text-sm text-muted-foreground">Featured</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Search & Filter</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search properties by title, address, or MLS number..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex space-x-2">
              <Select
                value={queryParams.status || 'all'}
                onValueChange={handleStatusFilter}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  {Object.values(PropertyStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={queryParams.propertyType || 'all'}
                onValueChange={handleTypeFilter}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.values(PropertyType).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <AdvancedSearchDialog
                onSearch={handleAdvancedSearch}
                currentParams={queryParams}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Properties Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Properties List</CardTitle>
            {pagination && (
              <Badge variant="outline">
                {pagination.total} total properties
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <LoadingState type="table" />
          ) : properties.length === 0 ? (
            debouncedQuery ? (
              <EmptyState
                type="no-search-results"
                onClearSearch={handleClearSearch}
              />
            ) : (queryParams.status || queryParams.propertyType) ? (
              <EmptyState
                type="no-filtered-results"
                onClearFilters={handleClearFilters}
              />
            ) : (
              <EmptyState
                type="no-clients"
                onAddClient={handleAddProperty}
              />
            )
          ) : (
            <PropertiesDataTable
              data={properties}
              onEdit={handleEditProperty}
              onRefresh={refetch}
            />
          )}
        </CardContent>
      </Card>

      {/* Property Form Dialog */}
      <PropertyFormDialog
        open={isFormDialogOpen}
        onOpenChange={setIsFormDialogOpen}
        property={selectedProperty}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
}
