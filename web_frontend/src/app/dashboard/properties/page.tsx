'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import axios from 'axios';
import { PropertyTable } from '@/components/property-table';
import { PropertyFormDialog } from '@/components/property-form-dialog';

interface Property {
  id: string;
  title: string;
  city: string;
  state: string;
  listPrice?: number;
  propertyType: string;
  status: string;
  assignee?: {
    firstName: string;
    lastName: string;
    email: string;
  };
}

export default function PropertiesPage() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProperty, setEditingProperty] = useState<Property | null>(null);
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('token');

    if (!token) {
      router.push('/login');
      return;
    }

    axios
      .get('http://localhost:3000/api/properties', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((res) => {
        setProperties(res.data.data.properties);
      })
      .catch((err) => {
        console.error('Failed to fetch properties:', err);
      })
      .finally(() => setLoading(false));
  }, [router]);

  const handleAddProperty = () => {
    setEditingProperty(null);
    setIsDialogOpen(true);
  };

  const handleEditProperty = (property: Property) => {
    setEditingProperty(property);
    setIsDialogOpen(true);
  };

  const handleDialogSuccess = () => {
    // Reload properties data
    const token = localStorage.getItem('token');

    if (!token) {
      router.push('/login');
      return;
    }

    axios
      .get('http://localhost:3000/api/properties', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((res) => {
        setProperties(res.data.data.properties);
      })
      .catch((err) => {
        console.error('Failed to fetch properties:', err);
      });
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-semibold">All Properties</h1>
        <Button onClick={handleAddProperty}>
          + Add New Property
        </Button>
      </div>

      <Card>
        <CardContent>
          {loading ? (
            <p>Loading...</p>
          ) : (
            <PropertyTable data={properties} onEdit={handleEditProperty} />
          )}
        </CardContent>
      </Card>

      <PropertyFormDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        property={editingProperty}
        onSuccess={handleDialogSuccess}
      />
    </div>
  );
}
