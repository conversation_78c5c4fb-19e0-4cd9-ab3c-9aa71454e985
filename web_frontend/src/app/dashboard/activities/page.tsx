
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { IconPlus, IconSearch, IconPhone, IconMail, IconCalendar, IconCheckbox } from "@tabler/icons-react"
import { Input } from "@/components/ui/input"

export default function ActivitiesPage() {
    return (

        <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
                <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                    <div className="px-4 lg:px-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">Activities</h1>
                                <p className="text-muted-foreground">
                                    Track tasks, meetings, calls, and other activities
                                </p>
                            </div>
                            <Button>
                                <IconPlus className="mr-2 h-4 w-4" />
                                Add Activity
                            </Button>
                        </div>
                    </div>

                    <div className="px-4 lg:px-6">
                        <div className="flex items-center space-x-2">
                            <div className="relative flex-1 max-w-sm">
                                <IconSearch className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input placeholder="Search activities..." className="pl-8" />
                            </div>
                        </div>
                    </div>

                    <div className="px-4 lg:px-6">
                        <div className="grid gap-4">
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <IconPhone className="h-4 w-4 text-blue-500" />
                                            <div>
                                                <CardTitle className="text-base">Follow-up call with John Smith</CardTitle>
                                                <CardDescription>Acme Corporation</CardDescription>
                                            </div>
                                        </div>
                                        <Badge variant="destructive">Overdue</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Due: Dec 10, 2024 at 2:00 PM</p>
                                        <p>Discuss contract terms and pricing options</p>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <IconCalendar className="h-4 w-4 text-green-500" />
                                            <div>
                                                <CardTitle className="text-base">Product demo meeting</CardTitle>
                                                <CardDescription>Global Solutions Inc</CardDescription>
                                            </div>
                                        </div>
                                        <Badge variant="default">Today</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Due: Dec 12, 2024 at 10:00 AM</p>
                                        <p>Present marketing automation features</p>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <IconMail className="h-4 w-4 text-purple-500" />
                                            <div>
                                                <CardTitle className="text-base">Send proposal document</CardTitle>
                                                <CardDescription>Tech Innovations LLC</CardDescription>
                                            </div>
                                        </div>
                                        <Badge variant="secondary">Tomorrow</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Due: Dec 13, 2024 at 9:00 AM</p>
                                        <p>Send custom development proposal with timeline</p>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2">
                                            <IconCheckbox className="h-4 w-4 text-gray-400" />
                                            <div>
                                                <CardTitle className="text-base line-through text-muted-foreground">Initial discovery call</CardTitle>
                                                <CardDescription>StartupX Inc</CardDescription>
                                            </div>
                                        </div>
                                        <Badge variant="outline">Completed</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Completed: Dec 8, 2024 at 3:00 PM</p>
                                        <p>Identified pain points and solution requirements</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    )
} 