
import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { IconPlus, IconSearch } from "@tabler/icons-react"
import { Input } from "@/components/ui/input"

export default function LeadsPage() {
    return (

        <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
                <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                    <div className="px-4 lg:px-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">Leads</h1>
                                <p className="text-muted-foreground">
                                    Track and manage your sales leads
                                </p>
                            </div>
                            <Button>
                                <IconPlus className="mr-2 h-4 w-4" />
                                Add Lead
                            </Button>
                        </div>
                    </div>

                    <div className="px-4 lg:px-6">
                        <div className="flex items-center space-x-2">
                            <div className="relative flex-1 max-w-sm">
                                <IconSearch className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input placeholder="Search leads..." className="pl-8" />
                            </div>
                        </div>
                    </div>

                    <div className="px-4 lg:px-6">
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Jennifer Wilson</CardTitle>
                                            <CardDescription>StartupX Inc</CardDescription>
                                        </div>
                                        <Badge variant="default">Hot</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Email: <EMAIL></p>
                                        <p>Phone: (*************</p>
                                        <p>Source: Website</p>
                                        <p>Value: $25,000</p>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Robert Brown</CardTitle>
                                            <CardDescription>Enterprise Corp</CardDescription>
                                        </div>
                                        <Badge variant="secondary">Warm</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Email: <EMAIL></p>
                                        <p>Phone: (*************</p>
                                        <p>Source: Referral</p>
                                        <p>Value: $50,000</p>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle>Lisa Garcia</CardTitle>
                                            <CardDescription>Small Business Co</CardDescription>
                                        </div>
                                        <Badge variant="outline">Cold</Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-sm text-muted-foreground">
                                        <p>Email: <EMAIL></p>
                                        <p>Phone: (*************</p>
                                        <p>Source: Cold Call</p>
                                        <p>Value: $10,000</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    )
} 