'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import {
  Client,
  ClientFormData,
  ClientsQueryParams,
  ClientSearchParams,
  ClientUpdateData,
  UseClientsResult,
  UseClientResult,
} from '@/types/client';
import { ClientsAPI } from '@/lib/api/clients';

/**
 * Hook for fetching and managing multiple clients
 */
export function useClients(params: ClientsQueryParams = {}): UseClientsResult {
  const [clients, setClients] = useState<Client[]>([]);
  const [pagination, setPagination] = useState<UseClientsResult['pagination']>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize params to prevent infinite re-renders
  const memoizedParams = useMemo(() => {
    // Create a stable object with only defined values
    const stableParams: ClientsQueryParams = {};
    if (params.page !== undefined) stableParams.page = params.page;
    if (params.limit !== undefined) stableParams.limit = params.limit;
    if (params.search !== undefined && params.search !== '') stableParams.search = params.search;
    if (params.status !== undefined) stableParams.status = params.status;
    if (params.type !== undefined) stableParams.type = params.type;
    if (params.source !== undefined) stableParams.source = params.source;
    if (params.tags !== undefined) stableParams.tags = params.tags;
    if (params.sortBy !== undefined) stableParams.sortBy = params.sortBy;
    if (params.sortOrder !== undefined) stableParams.sortOrder = params.sortOrder;
    return stableParams;
  }, [
    params.page,
    params.limit,
    params.search,
    params.status,
    params.type,
    params.source,
    params.tags,
    params.sortBy,
    params.sortOrder,
  ]);

  const fetchClients = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await ClientsAPI.getClients(memoizedParams);
      setClients(response.data.clients);
      setPagination(response.data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch clients';
      setError(errorMessage);
      console.error('Error fetching clients:', err);
    } finally {
      setLoading(false);
    }
  }, [memoizedParams]);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  return {
    clients,
    pagination,
    loading,
    error,
    refetch: fetchClients,
  };
}

/**
 * Hook for fetching a single client
 */
export function useClient(id: string | null): UseClientResult {
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchClient = useCallback(async () => {
    if (!id) {
      setClient(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await ClientsAPI.getClient(id);
      setClient(response.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch client';
      setError(errorMessage);
      console.error('Error fetching client:', err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchClient();
  }, [fetchClient]);

  return {
    client,
    loading,
    error,
    refetch: fetchClient,
  };
}

/**
 * Hook for client CRUD operations
 */
export function useClientOperations() {
  const [loading, setLoading] = useState(false);

  const createClient = useCallback(async (clientData: ClientFormData): Promise<Client> => {
    setLoading(true);
    try {
      const response = await ClientsAPI.createClient(clientData);
      toast.success('Client created successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create client';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateClient = useCallback(async (id: string, updates: ClientUpdateData): Promise<Client> => {
    setLoading(true);
    try {
      const response = await ClientsAPI.updateClient(id, updates);
      toast.success('Client updated successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteClient = useCallback(async (id: string): Promise<void> => {
    setLoading(true);
    try {
      await ClientsAPI.deleteClient(id);
      toast.success('Client deleted successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createClient,
    updateClient,
    deleteClient,
    loading,
  };
}

/**
 * Hook for client search functionality
 */
export function useClientSearch() {
  const [results, setResults] = useState<Client[]>([]);
  const [pagination, setPagination] = useState<UseClientsResult['pagination']>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchClients = useCallback(async (params: ClientSearchParams) => {
    if (!params.query.trim()) {
      setResults([]);
      setPagination(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await ClientsAPI.searchClients(params);
      setResults(response.data.clients);
      setPagination(response.data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search clients';
      setError(errorMessage);
      console.error('Error searching clients:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearSearch = useCallback(() => {
    setResults([]);
    setPagination(null);
    setError(null);
  }, []);

  return {
    results,
    pagination,
    loading,
    error,
    searchClients,
    clearSearch,
  };
}

/**
 * Hook for bulk operations on clients
 */
export function useClientBulkOperations() {
  const [loading, setLoading] = useState(false);

  const bulkDelete = useCallback(async (clientIds: string[]): Promise<void> => {
    setLoading(true);
    try {
      await ClientsAPI.bulkDeleteClients(clientIds);
      toast.success(`Successfully deleted ${clientIds.length} client(s)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete clients';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkUpdateStatus = useCallback(async (
    clientIds: string[],
    status: ClientUpdateData['status']
  ): Promise<void> => {
    setLoading(true);
    try {
      await ClientsAPI.bulkUpdateStatus(clientIds, status);
      toast.success(`Successfully updated status for ${clientIds.length} client(s)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client status';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkUpdateType = useCallback(async (
    clientIds: string[],
    type: ClientUpdateData['type']
  ): Promise<void> => {
    setLoading(true);
    try {
      await ClientsAPI.bulkUpdateType(clientIds, type);
      toast.success(`Successfully updated type for ${clientIds.length} client(s)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client type';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkAddTags = useCallback(async (
    clientIds: string[],
    tags: string[]
  ): Promise<void> => {
    setLoading(true);
    try {
      await ClientsAPI.bulkAddTags(clientIds, tags);
      toast.success(`Successfully added tags to ${clientIds.length} client(s)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add tags';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkRemoveTags = useCallback(async (
    clientIds: string[],
    tags: string[]
  ): Promise<void> => {
    setLoading(true);
    try {
      await ClientsAPI.bulkRemoveTags(clientIds, tags);
      toast.success(`Successfully removed tags from ${clientIds.length} client(s)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove tags';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    bulkDelete,
    bulkUpdateStatus,
    bulkUpdateType,
    bulkAddTags,
    bulkRemoveTags,
    loading,
  };
}

/**
 * Hook for debounced search
 */
export function useDebouncedClientSearch(delay: number = 300) {
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [query, setQuery] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => clearTimeout(timer);
  }, [query, delay]);

  return {
    query,
    debouncedQuery,
    setQuery,
  };
}
