'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import {
  Property,
  PropertyFormData,
  PropertiesQueryParams,
  PropertySearchParams,
  PropertyUpdateData,
  UsePropertiesResult,
  UsePropertyResult,
} from '@/types/property';
import { PropertiesAPI } from '@/lib/api/properties';

/**
 * Hook for fetching and managing multiple properties
 */
export function useProperties(params: PropertiesQueryParams = {}): UsePropertiesResult {
  const [properties, setProperties] = useState<Property[]>([]);
  const [pagination, setPagination] = useState<UsePropertiesResult['pagination']>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize params to prevent infinite re-renders
  const memoizedParams = useMemo(() => {
    // Create a stable object with only defined values
    const stableParams: PropertiesQueryParams = {};
    if (params.page !== undefined) stableParams.page = params.page;
    if (params.limit !== undefined) stableParams.limit = params.limit;
    if (params.search !== undefined && params.search !== '') stableParams.search = params.search;
    if (params.city !== undefined) stableParams.city = params.city;
    if (params.state !== undefined) stableParams.state = params.state;
    if (params.zipCode !== undefined) stableParams.zipCode = params.zipCode;
    if (params.propertyType !== undefined) stableParams.propertyType = params.propertyType;
    if (params.status !== undefined) stableParams.status = params.status;
    if (params.assigneeId !== undefined) stableParams.assigneeId = params.assigneeId;
    if (params.minSquareMeters !== undefined) stableParams.minSquareMeters = params.minSquareMeters;
    if (params.maxSquareMeters !== undefined) stableParams.maxSquareMeters = params.maxSquareMeters;
    if (params.minLotSize !== undefined) stableParams.minLotSize = params.minLotSize;
    if (params.maxLotSize !== undefined) stableParams.maxLotSize = params.maxLotSize;
    if (params.minBedrooms !== undefined) stableParams.minBedrooms = params.minBedrooms;
    if (params.maxBedrooms !== undefined) stableParams.maxBedrooms = params.maxBedrooms;
    if (params.minBathrooms !== undefined) stableParams.minBathrooms = params.minBathrooms;
    if (params.maxBathrooms !== undefined) stableParams.maxBathrooms = params.maxBathrooms;
    if (params.minPrice !== undefined) stableParams.minPrice = params.minPrice;
    if (params.maxPrice !== undefined) stableParams.maxPrice = params.maxPrice;
    if (params.priceType !== undefined) stableParams.priceType = params.priceType;
    if (params.listedAfter !== undefined) stableParams.listedAfter = params.listedAfter;
    if (params.listedBefore !== undefined) stableParams.listedBefore = params.listedBefore;
    if (params.updatedAfter !== undefined) stableParams.updatedAfter = params.updatedAfter;
    if (params.updatedBefore !== undefined) stableParams.updatedBefore = params.updatedBefore;
    if (params.features !== undefined) stableParams.features = params.features;
    if (params.amenities !== undefined) stableParams.amenities = params.amenities;
    if (params.isFeatured !== undefined) stableParams.isFeatured = params.isFeatured;
    if (params.isActive !== undefined) stableParams.isActive = params.isActive;
    if (params.sortBy !== undefined) stableParams.sortBy = params.sortBy;
    if (params.sortOrder !== undefined) stableParams.sortOrder = params.sortOrder;
    return stableParams;
  }, [
    params.page,
    params.limit,
    params.search,
    params.city,
    params.state,
    params.zipCode,
    params.propertyType,
    params.status,
    params.assigneeId,
    params.minSquareMeters,
    params.maxSquareMeters,
    params.minLotSize,
    params.maxLotSize,
    params.minBedrooms,
    params.maxBedrooms,
    params.minBathrooms,
    params.maxBathrooms,
    params.minPrice,
    params.maxPrice,
    params.priceType,
    params.listedAfter,
    params.listedBefore,
    params.updatedAfter,
    params.updatedBefore,
    params.features,
    params.amenities,
    params.isFeatured,
    params.isActive,
    params.sortBy,
    params.sortOrder,
  ]);

  const fetchProperties = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await PropertiesAPI.getProperties(memoizedParams);
      setProperties(response.data.properties);
      setPagination(response.data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch properties';
      setError(errorMessage);
      console.error('Error fetching properties:', err);
    } finally {
      setLoading(false);
    }
  }, [memoizedParams]);

  useEffect(() => {
    fetchProperties();
  }, [fetchProperties]);

  return {
    properties,
    pagination,
    loading,
    error,
    refetch: fetchProperties,
  };
}

/**
 * Hook for fetching a single property
 */
export function useProperty(id: string | null): UsePropertyResult {
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProperty = useCallback(async () => {
    if (!id) {
      setProperty(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await PropertiesAPI.getProperty(id);
      setProperty(response.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch property';
      setError(errorMessage);
      console.error('Error fetching property:', err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchProperty();
  }, [fetchProperty]);

  return {
    property,
    loading,
    error,
    refetch: fetchProperty,
  };
}

/**
 * Hook for property CRUD operations
 */
export function usePropertyOperations() {
  const [loading, setLoading] = useState(false);

  const createProperty = useCallback(async (propertyData: PropertyFormData): Promise<Property> => {
    setLoading(true);
    try {
      const response = await PropertiesAPI.createProperty(propertyData);
      toast.success('Property created successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create property';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateProperty = useCallback(async (id: string, updates: PropertyUpdateData): Promise<Property> => {
    setLoading(true);
    try {
      const response = await PropertiesAPI.updateProperty(id, updates);
      toast.success('Property updated successfully');
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update property';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProperty = useCallback(async (id: string): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.deleteProperty(id);
      toast.success('Property deleted successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete property';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createProperty,
    updateProperty,
    deleteProperty,
    loading,
  };
}

/**
 * Hook for property search functionality
 */
export function usePropertySearch() {
  const [results, setResults] = useState<Property[]>([]);
  const [pagination, setPagination] = useState<UsePropertiesResult['pagination']>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchProperties = useCallback(async (params: PropertySearchParams) => {
    if (!params.query.trim()) {
      setResults([]);
      setPagination(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await PropertiesAPI.searchProperties(params);
      setResults(response.data.properties);
      setPagination(response.data.pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search properties';
      setError(errorMessage);
      console.error('Error searching properties:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearSearch = useCallback(() => {
    setResults([]);
    setPagination(null);
    setError(null);
  }, []);

  return {
    results,
    pagination,
    loading,
    error,
    searchProperties,
    clearSearch,
  };
}

/**
 * Hook for bulk operations on properties
 */
export function usePropertyBulkOperations() {
  const [loading, setLoading] = useState(false);

  const bulkDelete = useCallback(async (propertyIds: string[]): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.bulkDeleteProperties(propertyIds);
      toast.success(`Successfully deleted ${propertyIds.length} property(ies)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete properties';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkUpdateStatus = useCallback(async (
    propertyIds: string[],
    status: PropertyUpdateData['status']
  ): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.bulkUpdateStatus(propertyIds, status);
      toast.success(`Successfully updated status for ${propertyIds.length} property(ies)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update property status';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkUpdateType = useCallback(async (
    propertyIds: string[],
    propertyType: PropertyUpdateData['propertyType']
  ): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.bulkUpdateType(propertyIds, propertyType);
      toast.success(`Successfully updated type for ${propertyIds.length} property(ies)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update property type';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkUpdateAssignee = useCallback(async (
    propertyIds: string[],
    assigneeId: string | undefined
  ): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.bulkUpdateAssignee(propertyIds, assigneeId);
      toast.success(`Successfully updated assignee for ${propertyIds.length} property(ies)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update property assignee';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkAddFeatures = useCallback(async (
    propertyIds: string[],
    features: string[]
  ): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.bulkAddFeatures(propertyIds, features);
      toast.success(`Successfully added features to ${propertyIds.length} property(ies)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add features';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkRemoveFeatures = useCallback(async (
    propertyIds: string[],
    features: string[]
  ): Promise<void> => {
    setLoading(true);
    try {
      await PropertiesAPI.bulkRemoveFeatures(propertyIds, features);
      toast.success(`Successfully removed features from ${propertyIds.length} property(ies)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove features';
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    bulkDelete,
    bulkUpdateStatus,
    bulkUpdateType,
    bulkUpdateAssignee,
    bulkAddFeatures,
    bulkRemoveFeatures,
    loading,
  };
}

/**
 * Hook for debounced search
 */
export function useDebouncedPropertySearch(delay: number = 300) {
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [query, setQuery] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, delay);

    return () => clearTimeout(timer);
  }, [query, delay]);

  return {
    query,
    debouncedQuery,
    setQuery,
  };
}
