import {
  PropertyFormData,
  PropertiesResponse,
  PropertyResponse,
  PropertyErrorResponse,
  PropertiesQueryParams,
  PropertySearchParams,
  PropertyUpdateData,
} from '@/types/property';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3004';
const PROPERTIES_ENDPOINT = `${API_BASE_URL}/properties`;

// Helper function to get auth token
const getAuthToken = (): string => {
  if (typeof window === 'undefined') {
    throw new Error('Cannot access localStorage on server');
  }
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Access token not found');
  }
  return token;
};

// Helper function to create headers
const createHeaders = (includeAuth = true): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    headers.Authorization = `Bearer ${getAuthToken()}`;
  }
  
  return headers;
};

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData: PropertyErrorResponse = await response.json().catch(() => ({
      success: false,
      message: 'Network error occurred',
      statusCode: response.status,
    }));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
};

// Helper function to build query string
const buildQueryString = (params: Record<string, unknown>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        searchParams.append(key, value.join(','));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });
  
  return searchParams.toString();
};

// API service class
export class PropertiesAPI {
  /**
   * Get all properties with optional filtering and pagination
   */
  static async getProperties(params: PropertiesQueryParams = {}): Promise<PropertiesResponse> {
    const queryString = buildQueryString(params as Record<string, unknown>);
    const url = queryString ? `${PROPERTIES_ENDPOINT}?${queryString}` : PROPERTIES_ENDPOINT;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
      cache: 'no-store',
    });
    
    return handleResponse<PropertiesResponse>(response);
  }

  /**
   * Get a single property by ID
   */
  static async getProperty(id: string): Promise<PropertyResponse> {
    const response = await fetch(`${PROPERTIES_ENDPOINT}/${id}`, {
      method: 'GET',
      headers: createHeaders(),
      cache: 'no-store',
    });
    
    return handleResponse<PropertyResponse>(response);
  }

  /**
   * Create a new property
   */
  static async createProperty(propertyData: PropertyFormData): Promise<PropertyResponse> {
    const response = await fetch(PROPERTIES_ENDPOINT, {
      method: 'POST',
      headers: createHeaders(),
      body: JSON.stringify(propertyData),
    });
    
    return handleResponse<PropertyResponse>(response);
  }

  /**
   * Update an existing property
   */
  static async updateProperty(id: string, updates: PropertyUpdateData): Promise<PropertyResponse> {
    const response = await fetch(`${PROPERTIES_ENDPOINT}/${id}`, {
      method: 'PUT',
      headers: createHeaders(),
      body: JSON.stringify(updates),
    });
    
    return handleResponse<PropertyResponse>(response);
  }

  /**
   * Delete a property (soft delete)
   */
  static async deleteProperty(id: string): Promise<{ success: true; message: string }> {
    const response = await fetch(`${PROPERTIES_ENDPOINT}/${id}`, {
      method: 'DELETE',
      headers: createHeaders(),
    });
    
    return handleResponse<{ success: true; message: string }>(response);
  }

  /**
   * Search properties with advanced query
   */
  static async searchProperties(params: PropertySearchParams): Promise<PropertiesResponse> {
    const queryString = buildQueryString(params as unknown as Record<string, unknown>);
    const url = `${PROPERTIES_ENDPOINT}/search?${queryString}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
      cache: 'no-store',
    });
    
    return handleResponse<PropertiesResponse>(response);
  }

  /**
   * Bulk delete properties
   */
  static async bulkDeleteProperties(propertyIds: string[]): Promise<{ success: true; message: string }> {
    const deletePromises = propertyIds.map(id => this.deleteProperty(id));
    await Promise.all(deletePromises);
    
    return {
      success: true,
      message: `Successfully deleted ${propertyIds.length} property(ies)`,
    };
  }

  /**
   * Bulk update property status
   */
  static async bulkUpdateStatus(
    propertyIds: string[], 
    status: PropertyUpdateData['status']
  ): Promise<{ success: true; message: string }> {
    const updatePromises = propertyIds.map(id => this.updateProperty(id, { status }));
    await Promise.all(updatePromises);
    
    return {
      success: true,
      message: `Successfully updated status for ${propertyIds.length} property(ies)`,
    };
  }

  /**
   * Bulk update property type
   */
  static async bulkUpdateType(
    propertyIds: string[], 
    propertyType: PropertyUpdateData['propertyType']
  ): Promise<{ success: true; message: string }> {
    const updatePromises = propertyIds.map(id => this.updateProperty(id, { propertyType }));
    await Promise.all(updatePromises);
    
    return {
      success: true,
      message: `Successfully updated type for ${propertyIds.length} property(ies)`,
    };
  }

  /**
   * Bulk update property assignee
   */
  static async bulkUpdateAssignee(
    propertyIds: string[], 
    assigneeId: string | undefined
  ): Promise<{ success: true; message: string }> {
    const updatePromises = propertyIds.map(id => this.updateProperty(id, { assigneeId }));
    await Promise.all(updatePromises);
    
    return {
      success: true,
      message: `Successfully updated assignee for ${propertyIds.length} property(ies)`,
    };
  }

  /**
   * Add features to multiple properties
   */
  static async bulkAddFeatures(
    propertyIds: string[], 
    newFeatures: string[]
  ): Promise<{ success: true; message: string }> {
    // First, get all properties to merge features
    const propertyPromises = propertyIds.map(id => this.getProperty(id));
    const properties = await Promise.all(propertyPromises);
    
    const updatePromises = properties.map((propertyResponse, index) => {
      const property = propertyResponse.data;
      const existingFeatures = property.features || [];
      const mergedFeatures = [...new Set([...existingFeatures, ...newFeatures])];
      return this.updateProperty(propertyIds[index], { features: mergedFeatures });
    });
    
    await Promise.all(updatePromises);
    
    return {
      success: true,
      message: `Successfully added features to ${propertyIds.length} property(ies)`,
    };
  }

  /**
   * Remove features from multiple properties
   */
  static async bulkRemoveFeatures(
    propertyIds: string[], 
    featuresToRemove: string[]
  ): Promise<{ success: true; message: string }> {
    // First, get all properties to filter features
    const propertyPromises = propertyIds.map(id => this.getProperty(id));
    const properties = await Promise.all(propertyPromises);
    
    const updatePromises = properties.map((propertyResponse, index) => {
      const property = propertyResponse.data;
      const existingFeatures = property.features || [];
      const filteredFeatures = existingFeatures.filter(feature => !featuresToRemove.includes(feature));
      return this.updateProperty(propertyIds[index], { features: filteredFeatures });
    });
    
    await Promise.all(updatePromises);
    
    return {
      success: true,
      message: `Successfully removed features from ${propertyIds.length} property(ies)`,
    };
  }
}

// Export individual functions for convenience
export const {
  getProperties,
  getProperty,
  createProperty,
  updateProperty,
  deleteProperty,
  searchProperties,
  bulkDeleteProperties,
  bulkUpdateStatus,
  bulkUpdateType,
  bulkUpdateAssignee,
  bulkAddFeatures,
  bulkRemoveFeatures,
} = PropertiesAPI;
