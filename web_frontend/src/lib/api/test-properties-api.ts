// Test file to verify properties API configuration
// This file can be deleted after testing

import { PropertiesAPI } from './properties';

export async function testPropertiesAPI() {
  try {
    console.log('Testing Properties API configuration...');
    console.log('API Base URL:', process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000');
    console.log('Properties Endpoint: /api/properties');
    
    // This will fail with 401 Unauthorized, which is expected
    // The important thing is that we get a proper error response, not a network error
    await PropertiesAPI.getProperties();
  } catch (error) {
    if (error instanceof Error) {
      console.log('Expected error (should be authentication related):', error.message);
      
      // If we get "Access token required" or similar, the API is working
      if (error.message.includes('Access token') || error.message.includes('Authentication') || error.message.includes('Unauthorized')) {
        console.log('✅ API configuration is correct - authentication error is expected');
        return true;
      } else {
        console.log('❌ Unexpected error:', error.message);
        return false;
      }
    }
  }
  
  return false;
}

// Export for testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testPropertiesAPI = testPropertiesAPI;
}
