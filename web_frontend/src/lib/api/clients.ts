import {
  ClientFormData,
  ClientsResponse,
  ClientResponse,
  ClientErrorResponse,
  ClientsQueryParams,
  ClientSearchParams,
  ClientUpdateData,
} from '@/types/client';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
const CLIENTS_ENDPOINT = `${API_BASE_URL}/api/clients`;

// Helper function to get auth token
const getAuthToken = (): string => {
  if (typeof window === 'undefined') {
    throw new Error('Cannot access localStorage on server');
  }
  const token = localStorage.getItem('token');
  if (!token) {
    throw new Error('Access token not found');
  }
  return token;
};

// Helper function to create headers
const createHeaders = (includeAuth = true): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (includeAuth) {
    headers.Authorization = `Bearer ${getAuthToken()}`;
  }

  return headers;
};

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData: ClientErrorResponse = await response.json().catch(() => ({
      success: false,
      message: 'Network error occurred',
      statusCode: response.status,
    }));
    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

// Helper function to build query string
const buildQueryString = (params: Record<string, unknown>): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        searchParams.append(key, value.join(','));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });

  return searchParams.toString();
};

// API service class
export class ClientsAPI {
  /**
   * Get all clients with optional filtering and pagination
   */
  static async getClients(params: ClientsQueryParams = {}): Promise<ClientsResponse> {
    const queryString = buildQueryString(params as Record<string, unknown>);
    const url = queryString ? `${CLIENTS_ENDPOINT}?${queryString}` : CLIENTS_ENDPOINT;

    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
      cache: 'no-store',
    });

    return handleResponse<ClientsResponse>(response);
  }

  /**
   * Get a single client by ID
   */
  static async getClient(id: string): Promise<ClientResponse> {
    const response = await fetch(`${CLIENTS_ENDPOINT}/${id}`, {
      method: 'GET',
      headers: createHeaders(),
      cache: 'no-store',
    });

    return handleResponse<ClientResponse>(response);
  }

  /**
   * Create a new client
   */
  static async createClient(clientData: ClientFormData): Promise<ClientResponse> {
    const response = await fetch(CLIENTS_ENDPOINT, {
      method: 'POST',
      headers: createHeaders(),
      body: JSON.stringify(clientData),
    });

    return handleResponse<ClientResponse>(response);
  }

  /**
   * Update an existing client
   */
  static async updateClient(id: string, updates: ClientUpdateData): Promise<ClientResponse> {
    const response = await fetch(`${CLIENTS_ENDPOINT}/${id}`, {
      method: 'PUT',
      headers: createHeaders(),
      body: JSON.stringify(updates),
    });

    return handleResponse<ClientResponse>(response);
  }

  /**
   * Delete a client (soft delete)
   */
  static async deleteClient(id: string): Promise<{ success: true; message: string }> {
    const response = await fetch(`${CLIENTS_ENDPOINT}/${id}`, {
      method: 'DELETE',
      headers: createHeaders(),
    });

    return handleResponse<{ success: true; message: string }>(response);
  }

  /**
   * Search clients with advanced query
   */
  static async searchClients(params: ClientSearchParams): Promise<ClientsResponse> {
    const queryString = buildQueryString(params as unknown as Record<string, unknown>);
    const url = `${CLIENTS_ENDPOINT}/search?${queryString}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: createHeaders(),
      cache: 'no-store',
    });

    return handleResponse<ClientsResponse>(response);
  }

  /**
   * Bulk delete clients
   */
  static async bulkDeleteClients(clientIds: string[]): Promise<{ success: true; message: string }> {
    const deletePromises = clientIds.map(id => this.deleteClient(id));
    await Promise.all(deletePromises);

    return {
      success: true,
      message: `Successfully deleted ${clientIds.length} client(s)`,
    };
  }

  /**
   * Bulk update client status
   */
  static async bulkUpdateStatus(
    clientIds: string[],
    status: ClientUpdateData['status']
  ): Promise<{ success: true; message: string }> {
    const updatePromises = clientIds.map(id => this.updateClient(id, { status }));
    await Promise.all(updatePromises);

    return {
      success: true,
      message: `Successfully updated status for ${clientIds.length} client(s)`,
    };
  }

  /**
   * Bulk update client type
   */
  static async bulkUpdateType(
    clientIds: string[],
    type: ClientUpdateData['type']
  ): Promise<{ success: true; message: string }> {
    const updatePromises = clientIds.map(id => this.updateClient(id, { type }));
    await Promise.all(updatePromises);

    return {
      success: true,
      message: `Successfully updated type for ${clientIds.length} client(s)`,
    };
  }

  /**
   * Add tags to multiple clients
   */
  static async bulkAddTags(
    clientIds: string[],
    newTags: string[]
  ): Promise<{ success: true; message: string }> {
    // First, get all clients to merge tags
    const clientPromises = clientIds.map(id => this.getClient(id));
    const clients = await Promise.all(clientPromises);

    const updatePromises = clients.map((clientResponse, index) => {
      const client = clientResponse.data;
      const existingTags = client.tags || [];
      const mergedTags = [...new Set([...existingTags, ...newTags])];
      return this.updateClient(clientIds[index], { tags: mergedTags });
    });

    await Promise.all(updatePromises);

    return {
      success: true,
      message: `Successfully added tags to ${clientIds.length} client(s)`,
    };
  }

  /**
   * Remove tags from multiple clients
   */
  static async bulkRemoveTags(
    clientIds: string[],
    tagsToRemove: string[]
  ): Promise<{ success: true; message: string }> {
    // First, get all clients to filter tags
    const clientPromises = clientIds.map(id => this.getClient(id));
    const clients = await Promise.all(clientPromises);

    const updatePromises = clients.map((clientResponse, index) => {
      const client = clientResponse.data;
      const existingTags = client.tags || [];
      const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));
      return this.updateClient(clientIds[index], { tags: filteredTags });
    });

    await Promise.all(updatePromises);

    return {
      success: true,
      message: `Successfully removed tags from ${clientIds.length} client(s)`,
    };
  }
}

// Export individual functions for convenience
export const {
  getClients,
  getClient,
  createClient,
  updateClient,
  deleteClient,
  searchClients,
  bulkDeleteClients,
  bulkUpdateStatus,
  bulkUpdateType,
  bulkAddTags,
  bulkRemoveTags,
} = ClientsAPI;
