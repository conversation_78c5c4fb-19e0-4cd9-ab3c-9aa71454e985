export interface Contact {
    id: string;
    name: string;
    email: string;
    phone: string;
    company: string;
    position: string;
    status: 'lead' | 'prospect' | 'customer';
    source: string;
    createdAt: string;
    lastContact: string;
}

export interface Deal {
    id: string;
    title: string;
    value: number;
    stage: 'prospecting' | 'qualification' | 'proposal' | 'negotiation' | 'closed-won' | 'closed-lost';
    probability: number;
    contactId: string;
    expectedCloseDate: string;
    createdAt: string;
    updatedAt: string;
}

export interface Activity {
    id: string;
    type: 'call' | 'email' | 'meeting' | 'note';
    title: string;
    description: string;
    contactId: string;
    dealId?: string;
    date: string;
    duration?: number; // in minutes
}

export interface DashboardMetrics {
    totalRevenue: number;
    monthlyRevenue: number;
    totalContacts: number;
    totalDeals: number;
    conversionRate: number;
    averageDealSize: number;
    dealsWonThisMonth: number;
    pipelineValue: number;
}

// Mock Contacts Data
export const mockContacts: Contact[] = [
    {
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'TechCorp Solutions',
        position: 'CTO',
        status: 'customer',
        source: 'Website',
        createdAt: '2024-01-15',
        lastContact: '2024-01-20'
    },
    {
        id: '2',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'InnovateTech',
        position: 'CEO',
        status: 'prospect',
        source: 'Referral',
        createdAt: '2024-01-18',
        lastContact: '2024-01-19'
    },
    {
        id: '3',
        name: 'Mike Chen',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'DigitalFlow Inc',
        position: 'VP of Sales',
        status: 'lead',
        source: 'Cold Outreach',
        createdAt: '2024-01-22',
        lastContact: '2024-01-22'
    },
    {
        id: '4',
        name: 'Emily Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'CloudVentures',
        position: 'Product Manager',
        status: 'prospect',
        source: 'LinkedIn',
        createdAt: '2024-01-10',
        lastContact: '2024-01-21'
    },
    {
        id: '5',
        name: 'David Wilson',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Smart Systems Ltd',
        position: 'Director of Technology',
        status: 'customer',
        source: 'Trade Show',
        createdAt: '2024-01-05',
        lastContact: '2024-01-18'
    }
];

// Mock Deals Data
export const mockDeals: Deal[] = [
    {
        id: '1',
        title: 'TechCorp Enterprise Package',
        value: 125000,
        stage: 'closed-won',
        probability: 100,
        contactId: '1',
        expectedCloseDate: '2024-01-30',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-25'
    },
    {
        id: '2',
        title: 'InnovateTech Solution Implementation',
        value: 75000,
        stage: 'negotiation',
        probability: 80,
        contactId: '2',
        expectedCloseDate: '2024-02-15',
        createdAt: '2024-01-18',
        updatedAt: '2024-01-23'
    },
    {
        id: '3',
        title: 'DigitalFlow CRM Integration',
        value: 45000,
        stage: 'qualification',
        probability: 40,
        contactId: '3',
        expectedCloseDate: '2024-03-01',
        createdAt: '2024-01-22',
        updatedAt: '2024-01-22'
    },
    {
        id: '4',
        title: 'CloudVentures Platform License',
        value: 95000,
        stage: 'proposal',
        probability: 65,
        contactId: '4',
        expectedCloseDate: '2024-02-28',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-21'
    },
    {
        id: '5',
        title: 'Smart Systems Consultation',
        value: 35000,
        stage: 'prospecting',
        probability: 25,
        contactId: '5',
        expectedCloseDate: '2024-04-15',
        createdAt: '2024-01-20',
        updatedAt: '2024-01-20'
    }
];

// Mock Activities Data
export const mockActivities: Activity[] = [
    {
        id: '1',
        type: 'call',
        title: 'Discovery Call with John Smith',
        description: 'Discussed requirements for enterprise package implementation',
        contactId: '1',
        dealId: '1',
        date: '2024-01-20',
        duration: 45
    },
    {
        id: '2',
        type: 'email',
        title: 'Proposal sent to Sarah Johnson',
        description: 'Sent detailed proposal for InnovateTech solution',
        contactId: '2',
        dealId: '2',
        date: '2024-01-19'
    },
    {
        id: '3',
        type: 'meeting',
        title: 'Initial meeting with Mike Chen',
        description: 'First meeting to understand DigitalFlow requirements',
        contactId: '3',
        dealId: '3',
        date: '2024-01-22',
        duration: 60
    },
    {
        id: '4',
        type: 'note',
        title: 'Follow-up required',
        description: 'Need to follow up on CloudVentures proposal by end of week',
        contactId: '4',
        dealId: '4',
        date: '2024-01-21'
    },
    {
        id: '5',
        type: 'call',
        title: 'Qualification call with David Wilson',
        description: 'Discussed budget and timeline for Smart Systems project',
        contactId: '5',
        dealId: '5',
        date: '2024-01-18',
        duration: 30
    }
];

// Mock Dashboard Metrics
export const mockDashboardMetrics: DashboardMetrics = {
    totalRevenue: 2450000,
    monthlyRevenue: 375000,
    totalContacts: 247,
    totalDeals: 42,
    conversionRate: 23.5,
    averageDealSize: 68500,
    dealsWonThisMonth: 8,
    pipelineValue: 1250000
};

// Helper functions to get related data
export const getContactById = (id: string): Contact | undefined => {
    return mockContacts.find(contact => contact.id === id);
};

export const getDealsByContactId = (contactId: string): Deal[] => {
    return mockDeals.filter(deal => deal.contactId === contactId);
};

export const getActivitiesByContactId = (contactId: string): Activity[] => {
    return mockActivities.filter(activity => activity.contactId === contactId);
};

export const getActivitiesByDealId = (dealId: string): Activity[] => {
    return mockActivities.filter(activity => activity.dealId === dealId);
}; 