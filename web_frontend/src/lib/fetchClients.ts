import { z } from "zod"

// مخطط البيانات
export const clientSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  status: z.string().optional(),
  type: z.string().optional(),
})

export const clientsSchema = z.array(clientSchema)
export type Client = z.infer<typeof clientSchema>

export async function fetchClients(): Promise<Client[]> {
  try {
    // تأكد إن الكود بيشتغل في المتصفح مش على السيرفر
    if (typeof window === "undefined") throw new Error("Cannot access localStorage on server")

    const token = localStorage.getItem("token")

    if (!token) {
      throw new Error("Access token not found")
    }

    const res = await fetch("http://localhost:3000/api/clients", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      cache: "no-store",
    })

    if (!res.ok) {
      const errorBody = await res.text()
      console.error("Error Response Body:", errorBody)
      throw new Error("Failed to fetch clients")
    }

    const json = await res.json()
    console.log("API response:", json.data.clients)

    return clientsSchema.parse(json.data.clients)
  } catch (err) {
    console.error("Error fetching clients:", err)
    throw err
  }
}
