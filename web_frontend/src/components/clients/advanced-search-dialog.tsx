'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Search, Filter } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import {
  ClientsQueryParams,
  ClientStatus,
  ClientType,
  LeadSource,

} from '@/types/client';

// Advanced search form schema
const advancedSearchSchema = z.object({
  search: z.string().optional(),
  status: z.nativeEnum(ClientStatus).optional(),
  type: z.nativeEnum(ClientType).optional(),
  source: z.nativeEnum(LeadSource).optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  budgetMin: z.number().positive().optional(),
  budgetMax: z.number().positive().optional(),
  propertyTypes: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  hasEmail: z.boolean().optional(),
  hasPhone: z.boolean().optional(),
  consentGiven: z.boolean().optional(),
  marketingOptIn: z.boolean().optional(),
  createdAfter: z.string().optional(),
  createdBefore: z.string().optional(),
});

type AdvancedSearchValues = z.infer<typeof advancedSearchSchema>;

interface AdvancedSearchDialogProps {
  onSearch: (params: ClientsQueryParams) => void;
  currentParams: ClientsQueryParams;
}

export function AdvancedSearchDialog({ onSearch, currentParams }: AdvancedSearchDialogProps) {
  const [open, setOpen] = useState(false);


  const form = useForm<AdvancedSearchValues>({
    resolver: zodResolver(advancedSearchSchema),
    defaultValues: {
      search: currentParams.search || '',
      status: currentParams.status,
      type: currentParams.type,
      source: currentParams.source,
      city: '',
      state: '',
      country: '',
      budgetMin: undefined,
      budgetMax: undefined,
      propertyTypes: [],
      tags: [],
      hasEmail: undefined,
      hasPhone: undefined,
      consentGiven: undefined,
      marketingOptIn: undefined,
      createdAfter: '',
      createdBefore: '',
    },
  });

  const onSubmit = (data: AdvancedSearchValues) => {
    // Convert form data to query parameters
    const queryParams: ClientsQueryParams = {
      ...currentParams,
      search: data.search || undefined,
      status: data.status,
      type: data.type,
      source: data.source,
      page: 1, // Reset to first page
    };

    // Add additional filters as tags or custom parameters
    // Note: The backend would need to support these additional filters

    onSearch(queryParams);
    setOpen(false);
  };

  const handleReset = () => {
    form.reset({
      search: '',
      status: undefined,
      type: undefined,
      source: undefined,
      city: '',
      state: '',
      country: '',
      budgetMin: undefined,
      budgetMax: undefined,
      propertyTypes: [],
      tags: [],
      hasEmail: undefined,
      hasPhone: undefined,
      consentGiven: undefined,
      marketingOptIn: undefined,
      createdAfter: '',
      createdBefore: '',
    });
  };

  // Note: Tag functionality would be implemented when needed
  // const addTag = () => {
  //   if (newTag.trim() && !form.getValues('tags')?.includes(newTag.trim())) {
  //     const currentTags = form.getValues('tags') || [];
  //     form.setValue('tags', [...currentTags, newTag.trim()]);
  //     setNewTag('');
  //   }
  // };

  // const removeTag = (tagToRemove: string) => {
  //   const currentTags = form.getValues('tags') || [];
  //   form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));
  // };

  const hasActiveFilters = () => {
    const values = form.getValues();
    return Object.entries(values).some(([key, value]) => {
      if (key === 'search') return false; // Search is handled separately
      if (Array.isArray(value)) return value.length > 0;
      return value !== undefined && value !== '';
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Filter className="h-4 w-4 mr-2" />
          Advanced Search
          {hasActiveFilters() && (
            <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs">
              !
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Advanced Search</span>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Search */}
            <FormField
              control={form.control}
              name="search"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Search Text</FormLabel>
                  <FormControl>
                    <Input placeholder="Search by name, email, or phone..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Client Classification */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Client Classification</h3>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Any status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Any Status</SelectItem>
                          {Object.values(ClientStatus).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Any type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Any Type</SelectItem>
                          {Object.values(ClientType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="source"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Source</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Any source" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Any Source</SelectItem>
                          {Object.values(LeadSource).map((source) => (
                            <SelectItem key={source} value={source}>
                              {source.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Location */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Location</h3>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter city" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter state" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <FormControl>
                        <Input placeholder="US" maxLength={2} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Budget Range */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Budget Range</h3>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="budgetMin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Budget</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="budgetMax"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum Budget</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="No limit"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset
              </Button>
              <Button type="button" variant="ghost" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Apply Filters
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
