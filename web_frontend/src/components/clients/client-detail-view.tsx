'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Mail,
  Phone,
  MapPin,
  DollarSign,
  Calendar,
  User,
  Building,
  Tag,

  Shield,
  MessageSquare,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

import { Client, ClientStatus, ClientType } from '@/types/client';
import { useClientOperations } from '@/hooks/use-clients';

interface ClientDetailViewProps {
  client: Client;
  onEdit: () => void;
  onDeleted: () => void;
}

export function ClientDetailView({ client, onEdit, onDeleted }: ClientDetailViewProps) {
  const router = useRouter();
  const { deleteClient, loading } = useClientOperations();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteClient(client.id);
      onDeleted();
      router.push('/dashboard/clients');
    } catch (error) {
      console.error('Delete error:', error);
    }
    setDeleteDialogOpen(false);
  };

  const getStatusBadgeVariant = (status: ClientStatus) => {
    switch (status) {
      case ClientStatus.ACTIVE:
        return 'default';
      case ClientStatus.CONVERTED:
        return 'default';
      case ClientStatus.NURTURING:
        return 'secondary';
      case ClientStatus.INACTIVE:
        return 'outline';
      case ClientStatus.LOST:
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getTypeBadgeVariant = (type: ClientType) => {
    switch (type) {
      case ClientType.BUYER:
      case ClientType.SELLER:
        return 'default';
      case ClientType.LEAD:
      case ClientType.PROSPECT:
        return 'secondary';
      case ClientType.INVESTOR:
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const initials = `${client.firstName[0]}${client.lastName[0]}`.toUpperCase();

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarFallback className="text-lg">{initials}</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-2xl font-bold">{`${client.firstName} ${client.lastName}`}</h1>
              <p className="text-muted-foreground">Client ID: {client.id.slice(-8)}</p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={onEdit} className="flex items-center space-x-2">
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </Button>
          <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" className="flex items-center space-x-2">
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the client
                  &quot;{client.firstName} {client.lastName}&quot; and remove their data from the system.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  disabled={loading}
                >
                  {loading ? 'Deleting...' : 'Delete'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Status and Type Badges */}
      <div className="flex items-center space-x-2">
        <Badge variant={getStatusBadgeVariant(client.status)} className="flex items-center space-x-1">
          <User className="h-3 w-3" />
          <span>{client.status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</span>
        </Badge>
        <Badge variant={getTypeBadgeVariant(client.type)} className="flex items-center space-x-1">
          <Building className="h-3 w-3" />
          <span>{client.type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</span>
        </Badge>
        {client.source && (
          <Badge variant="outline" className="flex items-center space-x-1">
            <MessageSquare className="h-3 w-3" />
            <span>{client.source.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</span>
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Contact Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {client.email && (
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">{client.email}</p>
                </div>
              </div>
            )}
            {client.phone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Phone</p>
                  <p className="text-sm text-muted-foreground">{client.phone}</p>
                </div>
              </div>
            )}
            {client.alternatePhone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Alternate Phone</p>
                  <p className="text-sm text-muted-foreground">{client.alternatePhone}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Address</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {client.address && <p className="text-sm">{client.address}</p>}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              {client.city && <span>{client.city}</span>}
              {client.city && client.state && <span>,</span>}
              {client.state && <span>{client.state}</span>}
              {client.zipCode && <span>{client.zipCode}</span>}
            </div>
            {client.country && (
              <p className="text-sm text-muted-foreground">Country: {client.country}</p>
            )}
          </CardContent>
        </Card>

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>Financial Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {client.budget ? (
              <div className="flex items-center space-x-3">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Budget</p>
                  <p className="text-lg font-semibold">{formatCurrency(client.budget)}</p>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No budget specified</p>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Property Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>Property Preferences</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {client.propertyTypes && client.propertyTypes.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Property Types</p>
                <div className="flex flex-wrap gap-2">
                  {client.propertyTypes.map((type) => (
                    <Badge key={type} variant="outline">
                      {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            {client.preferredAreas && client.preferredAreas.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Preferred Areas</p>
                <div className="flex flex-wrap gap-2">
                  {client.preferredAreas.map((area) => (
                    <Badge key={area} variant="secondary">
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tags and Notes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span>Tags & Notes</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {client.tags && client.tags.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Tags</p>
                <div className="flex flex-wrap gap-2">
                  {client.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            {client.notes && (
              <div>
                <p className="text-sm font-medium mb-2">Notes</p>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">{client.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Consent and Dates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Consent & Timeline</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Consent Given</p>
                <p className="text-sm text-muted-foreground">
                  {client.consentGiven ? 'Yes' : 'No'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Marketing Opt-in</p>
                <p className="text-sm text-muted-foreground">
                  {client.marketingOptIn ? 'Yes' : 'No'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Created</p>
                <p className="text-sm text-muted-foreground">{formatDate(client.createdAt)}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Last Updated</p>
                <p className="text-sm text-muted-foreground">{formatDate(client.updatedAt)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
