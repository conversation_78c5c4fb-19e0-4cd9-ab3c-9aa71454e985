'use client';

import { Users, Plus, Search, Filter } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface EmptyStateProps {
  type: 'no-clients' | 'no-search-results' | 'no-filtered-results';
  onAddClient?: () => void;
  onClearSearch?: () => void;
  onClearFilters?: () => void;
}

export function EmptyState({ type, onAddClient, onClearSearch, onClearFilters }: EmptyStateProps) {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'no-clients':
        return {
          icon: <Users className="h-16 w-16 text-muted-foreground" />,
          title: 'No clients yet',
          description: 'Get started by adding your first client to the system.',
          action: onAddClient && (
            <Button onClick={onAddClient} className="mt-4">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Client
            </Button>
          ),
        };
      
      case 'no-search-results':
        return {
          icon: <Search className="h-16 w-16 text-muted-foreground" />,
          title: 'No search results',
          description: 'We couldn\'t find any clients matching your search criteria. Try adjusting your search terms.',
          action: onClearSearch && (
            <Button variant="outline" onClick={onClearSearch} className="mt-4">
              Clear Search
            </Button>
          ),
        };
      
      case 'no-filtered-results':
        return {
          icon: <Filter className="h-16 w-16 text-muted-foreground" />,
          title: 'No matching clients',
          description: 'No clients match the current filters. Try adjusting your filter criteria.',
          action: onClearFilters && (
            <Button variant="outline" onClick={onClearFilters} className="mt-4">
              Clear Filters
            </Button>
          ),
        };
      
      default:
        return {
          icon: <Users className="h-16 w-16 text-muted-foreground" />,
          title: 'No data',
          description: 'No information available.',
          action: null,
        };
    }
  };

  const { icon, title, description, action } = getEmptyStateContent();

  return (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-16 text-center">
        <div className="mb-4">
          {icon}
        </div>
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground mb-4 max-w-md">
          {description}
        </p>
        {action}
      </CardContent>
    </Card>
  );
}
