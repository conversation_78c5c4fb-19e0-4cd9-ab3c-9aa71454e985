"use client"


import * as React from "react"
import { useRouter } from "next/navigation"
import {
  IconCreditCard,
  IconDotsVertical,
  IconLogout,
  IconNotification,
  IconUserCircle,
} from "@tabler/icons-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"

export function NavUser() {
  const { isMobile } = useSidebar()
  const router = useRouter()

  const [userData, setUserData] = React.useState<{
    firstName?: string
    lastName?: string
    email?: string
    role?: string
    avatar?: string
  }>({})

  React.useEffect(() => {
    if (typeof window !== "undefined") {
      const storedUser = localStorage.getItem("user") || sessionStorage.getItem("user")
      if (storedUser) {
        try {
          const parsed = JSON.parse(storedUser)
          setUserData(parsed)
        } catch (e) {
          console.error("Invalid user data:", e)
        }
      }
    }
  }, [])

  const name = `${userData.firstName || ""} ${userData.lastName || ""}`.trim()
  const email = userData.email || ""
  const role = userData.role || ""
  const avatar = userData.avatar || "/default-avatar.png"

  const handleLogout = () => {
    localStorage.removeItem("token")
    localStorage.removeItem("refreshToken")
    localStorage.removeItem("user")
    sessionStorage.removeItem("token")
    sessionStorage.removeItem("refreshToken")
    sessionStorage.removeItem("user")
    router.push("/signin")
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
<Avatar className="h-8 w-8 rounded-lg grayscale">
  <AvatarImage src={avatar} alt={name} />
  <AvatarFallback className="rounded-lg">CN</AvatarFallback>
</Avatar>
<div className="grid flex-1 text-left text-sm leading-tight">
  <span className="truncate font-medium">{name}</span>
  <span className="text-muted-foreground truncate text-xs">{email}</span>
  <span className="text-muted-foreground truncate text-xs uppercase">
    {role}
  </span>
</div>
<IconDotsVertical className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
  <Avatar className="h-8 w-8 rounded-lg">
    <AvatarImage src={avatar} alt={name} />
    <AvatarFallback className="rounded-lg">CN</AvatarFallback>
  </Avatar>
  <div className="grid flex-1 text-left text-sm leading-tight">
    <span className="truncate font-medium">{name}</span>
    <span className="text-muted-foreground truncate text-xs">{email}</span>
    <span className="text-muted-foreground truncate text-xs uppercase">
      {role}
    </span>
  </div>
</div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
<DropdownMenuGroup>
  <DropdownMenuItem onClick={() => router.push("/profile")}>
    <IconUserCircle />
    Account
  </DropdownMenuItem>
  <DropdownMenuItem onClick={() => router.push("/billing")}>
    <IconCreditCard />
    Billing
  </DropdownMenuItem>
  <DropdownMenuItem onClick={() => router.push("/notifications")}>
    <IconNotification />
    Notifications
  </DropdownMenuItem>
</DropdownMenuGroup>
<DropdownMenuSeparator />
<DropdownMenuItem onClick={handleLogout} variant="destructive">
  <IconLogout />
  Log out
</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
