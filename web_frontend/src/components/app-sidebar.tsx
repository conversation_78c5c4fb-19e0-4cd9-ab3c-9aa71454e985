"use client"

import * as React from "react"
import {
  IconCalendar,
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFileAi,
  IconFileDescription,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconMail,
  IconPhone,
  IconReport,
  IconSearch,
  IconSettings,
  IconUsers,
  IconTarget,
  IconActivity,
} from "@tabler/icons-react"

import { NavDocuments } from "@/components/nav-documents"
import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { LucideTableProperties } from "lucide-react"

const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/john.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard/",
      icon: IconDashboard,
    },
    {
      title: "Clients",
      url: "/dashboard/clients",
      icon: IconUsers,
    },
    {
      title: "properties",
      url: "/dashboard/properties",
      icon: LucideTableProperties,
    },
    {
      title: "Leads",
      url: "/dashboard/leads",
      icon: IconTarget,
    },
    {
      title: "Opportunities",
      url: "/dashboard/opportunities",
      icon: IconChartBar,
    },
    {
      title: "Activities",
      url: "/dashboard/activities",
      icon: IconActivity,
    },
  ],
  navClouds: [
    {
      title: "Communications",
      icon: IconMail,
      isActive: true,
      url: "#",
      items: [
        {
          title: "Email Templates",
          url: "#",
        },
        {
          title: "Call Logs",
          url: "#",
        },
      ],
    },
    {
      title: "Documents",
      icon: IconFileDescription,
      url: "#",
      items: [
        {
          title: "Proposals",
          url: "#",
        },
        {
          title: "Contracts",
          url: "#",
        },
      ],
    },
    {
      title: "Automation",
      icon: IconFileAi,
      url: "#",
      items: [
        {
          title: "Workflows",
          url: "#",
        },
        {
          title: "Triggers",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "#",
      icon: IconSettings,
    },
    {
      title: "Get Help",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: "CRM Data",
      url: "#",
      icon: IconDatabase,
    },
    {
      name: "Reports",
      url: "#",
      icon: IconReport,
    },
    {
      name: "Calendar",
      url: "#",
      icon: IconCalendar,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">Real Estate CRM</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
