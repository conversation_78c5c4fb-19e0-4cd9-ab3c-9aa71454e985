'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, Building, Plus, X } from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

import {
  Property,
  PropertyFormData,
  PropertyType,
  PropertyStatus,
} from '@/types/property';
import { usePropertyOperations } from '@/hooks/use-properties';

// Form validation schema
const propertyFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  description: z.string().optional(),
  propertyType: z.nativeEnum(PropertyType),
  status: z.nativeEnum(PropertyStatus).optional(),

  // Location
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipCode: z.string().optional(),
  country: z.string().length(2, 'Country code must be 2 characters').optional().or(z.literal('')),
  latitude: z.number().min(-90).max(90).optional().or(z.literal('')),
  longitude: z.number().min(-180).max(180).optional().or(z.literal('')),

  // Property Details
  bedrooms: z.number().min(0).optional().or(z.literal('')),
  bathrooms: z.number().min(0).optional().or(z.literal('')),
  squareMeters: z.number().min(0).optional().or(z.literal('')),
  lotSizeMeters: z.number().min(0).optional().or(z.literal('')),
  yearBuilt: z.number().min(1800).max(new Date().getFullYear() + 5).optional().or(z.literal('')),

  // Pricing
  listPrice: z.number().min(0).optional().or(z.literal('')),
  salePrice: z.number().min(0).optional().or(z.literal('')),
  rentPrice: z.number().min(0).optional().or(z.literal('')),
  pricePerSquareMeter: z.number().min(0).optional().or(z.literal('')),

  // Assignment
  assigneeId: z.string().optional(),

  // Features & Media
  features: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
  images: z.array(z.string().url()).optional(),
  virtualTourUrl: z.string().url().optional().or(z.literal('')),

  // Listing Details
  mlsNumber: z.string().optional(),
  listingDate: z.string().optional(),
  expirationDate: z.string().optional(),
  daysOnMarket: z.number().min(0).optional().or(z.literal('')),

  // Status
  isFeatured: z.boolean().optional(),
});

type PropertyFormValues = z.infer<typeof propertyFormSchema>;

interface PropertyFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  property?: Property | null;
  onSuccess: () => void;
}

export function PropertyFormDialog({ open, onOpenChange, property, onSuccess }: PropertyFormDialogProps) {
  const [newFeature, setNewFeature] = useState('');
  const [newAmenity, setNewAmenity] = useState('');
  const [newImage, setNewImage] = useState('');
  const { createProperty, updateProperty, loading } = usePropertyOperations();

  const isEditing = !!property;

  const form = useForm<PropertyFormValues>({
    resolver: zodResolver(propertyFormSchema),
    defaultValues: {
      title: '',
      description: '',
      propertyType: PropertyType.SINGLE_FAMILY,
      status: PropertyStatus.AVAILABLE,
      address: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US',
      latitude: undefined,
      longitude: undefined,
      bedrooms: undefined,
      bathrooms: undefined,
      squareMeters: undefined,
      lotSizeMeters: undefined,
      yearBuilt: undefined,
      listPrice: undefined,
      salePrice: undefined,
      rentPrice: undefined,
      pricePerSquareMeter: undefined,
      assigneeId: '',
      features: [],
      amenities: [],
      images: [],
      virtualTourUrl: '',
      mlsNumber: '',
      listingDate: '',
      expirationDate: '',
      daysOnMarket: undefined,
      isFeatured: false,
    },
  });

  // Reset form when dialog opens/closes or property changes
  useEffect(() => {
    if (open) {
      if (property) {
        form.reset({
          title: property.title,
          description: property.description || '',
          propertyType: property.propertyType,
          status: property.status,
          address: property.address,
          city: property.city,
          state: property.state,
          zipCode: property.zipCode || '',
          country: property.country || 'US',
          latitude: property.latitude,
          longitude: property.longitude,
          bedrooms: property.bedrooms,
          bathrooms: property.bathrooms,
          squareMeters: property.squareMeters,
          lotSizeMeters: property.lotSizeMeters,
          yearBuilt: property.yearBuilt,
          listPrice: property.listPrice,
          salePrice: property.salePrice,
          rentPrice: property.rentPrice,
          pricePerSquareMeter: property.pricePerSquareMeter,
          assigneeId: property.assigneeId || '',
          features: property.features || [],
          amenities: property.amenities || [],
          images: property.images || [],
          virtualTourUrl: property.virtualTourUrl || '',
          mlsNumber: property.mlsNumber || '',
          listingDate: property.listingDate ? property.listingDate.split('T')[0] : '',
          expirationDate: property.expirationDate ? property.expirationDate.split('T')[0] : '',
          daysOnMarket: property.daysOnMarket,
          isFeatured: property.isFeatured,
        });
      } else {
        form.reset({
          title: '',
          description: '',
          propertyType: PropertyType.SINGLE_FAMILY,
          status: PropertyStatus.AVAILABLE,
          address: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'US',
          latitude: undefined,
          longitude: undefined,
          bedrooms: undefined,
          bathrooms: undefined,
          squareMeters: undefined,
          lotSizeMeters: undefined,
          yearBuilt: undefined,
          listPrice: undefined,
          salePrice: undefined,
          rentPrice: undefined,
          pricePerSquareMeter: undefined,
          assigneeId: '',
          features: [],
          amenities: [],
          images: [],
          virtualTourUrl: '',
          mlsNumber: '',
          listingDate: '',
          expirationDate: '',
          daysOnMarket: undefined,
          isFeatured: false,
        });
      }
    }
  }, [open, property, form]);

  const onSubmit = async (data: PropertyFormValues) => {
    try {
      // Convert empty strings to undefined for optional fields
      const formData: PropertyFormData = {
        ...data,
        description: data.description || undefined,
        zipCode: data.zipCode || undefined,
        country: data.country || undefined,
        latitude: typeof data.latitude === 'number' ? data.latitude : undefined,
        longitude: typeof data.longitude === 'number' ? data.longitude : undefined,
        bedrooms: typeof data.bedrooms === 'number' ? data.bedrooms : undefined,
        bathrooms: typeof data.bathrooms === 'number' ? data.bathrooms : undefined,
        squareMeters: typeof data.squareMeters === 'number' ? data.squareMeters : undefined,
        lotSizeMeters: typeof data.lotSizeMeters === 'number' ? data.lotSizeMeters : undefined,
        yearBuilt: typeof data.yearBuilt === 'number' ? data.yearBuilt : undefined,
        listPrice: typeof data.listPrice === 'number' ? data.listPrice : undefined,
        salePrice: typeof data.salePrice === 'number' ? data.salePrice : undefined,
        rentPrice: typeof data.rentPrice === 'number' ? data.rentPrice : undefined,
        pricePerSquareMeter: typeof data.pricePerSquareMeter === 'number' ? data.pricePerSquareMeter : undefined,
        assigneeId: data.assigneeId || undefined,
        virtualTourUrl: data.virtualTourUrl || undefined,
        mlsNumber: data.mlsNumber || undefined,
        listingDate: data.listingDate || undefined,
        expirationDate: data.expirationDate || undefined,
        daysOnMarket: typeof data.daysOnMarket === 'number' ? data.daysOnMarket : undefined,
      };

      if (isEditing && property) {
        await updateProperty(property.id, formData);
      } else {
        await createProperty(formData);
      }

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the hook
      console.error('Form submission error:', error);
    }
  };

  const addFeature = () => {
    if (newFeature.trim() && !form.getValues('features')?.includes(newFeature.trim())) {
      const currentFeatures = form.getValues('features') || [];
      form.setValue('features', [...currentFeatures, newFeature.trim()]);
      setNewFeature('');
    }
  };

  const removeFeature = (featureToRemove: string) => {
    const currentFeatures = form.getValues('features') || [];
    form.setValue('features', currentFeatures.filter(feature => feature !== featureToRemove));
  };

  const addAmenity = () => {
    if (newAmenity.trim() && !form.getValues('amenities')?.includes(newAmenity.trim())) {
      const currentAmenities = form.getValues('amenities') || [];
      form.setValue('amenities', [...currentAmenities, newAmenity.trim()]);
      setNewAmenity('');
    }
  };

  const removeAmenity = (amenityToRemove: string) => {
    const currentAmenities = form.getValues('amenities') || [];
    form.setValue('amenities', currentAmenities.filter(amenity => amenity !== amenityToRemove));
  };

  const addImage = () => {
    if (newImage.trim() && !form.getValues('images')?.includes(newImage.trim())) {
      const currentImages = form.getValues('images') || [];
      form.setValue('images', [...currentImages, newImage.trim()]);
      setNewImage('');
    }
  };

  const removeImage = (imageToRemove: string) => {
    const currentImages = form.getValues('images') || [];
    form.setValue('images', currentImages.filter(image => image !== imageToRemove));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <div className="flex items-center gap-2">
            <Building className="h-5 w-5 text-muted-foreground" />
            <DialogTitle className="text-lg font-semibold">
              {isEditing ? 'Edit Property' : 'Add New Property'}
            </DialogTitle>
          </div>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Basic Information</h3>
              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter property title" {...field} disabled={loading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter property description"
                          className="min-h-[100px]"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Property Classification */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Property Classification</h3>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="propertyType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Type *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PropertyType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PropertyStatus).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Location Information */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Location Information</h3>
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter street address" {...field} disabled={loading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter city" {...field} disabled={loading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter state" {...field} disabled={loading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="zipCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ZIP Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter ZIP code" {...field} disabled={loading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country Code</FormLabel>
                      <FormControl>
                        <Input placeholder="US" maxLength={2} {...field} disabled={loading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="latitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="any"
                          placeholder="Enter latitude"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="longitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="any"
                          placeholder="Enter longitude"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Property Details */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Property Details</h3>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="bedrooms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bedrooms</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="bathrooms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bathrooms</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="0.5"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="yearBuilt"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Year Built</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1800"
                          max={new Date().getFullYear() + 5}
                          placeholder="2020"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="squareMeters"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Square Meters</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="100"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lotSizeMeters"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lot Size (Square Meters)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="500"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Pricing Information */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Pricing Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="listPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>List Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="350000"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="salePrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sale Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="340000"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="rentPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rent Price (Monthly)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="2500"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="pricePerSquareMeter"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price per Square Meter</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="3500"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Features */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Features</h3>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add feature (e.g., garage, garden)"
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                    disabled={loading}
                  />
                  <Button type="button" onClick={addFeature} disabled={loading} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {form.watch('features')?.map((feature) => (
                    <Badge key={feature} variant="secondary" className="flex items-center gap-1">
                      {feature}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeFeature(feature)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Amenities</h3>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    placeholder="Add amenity (e.g., pool, gym)"
                    value={newAmenity}
                    onChange={(e) => setNewAmenity(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity())}
                    disabled={loading}
                  />
                  <Button type="button" onClick={addAmenity} disabled={loading} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {form.watch('amenities')?.map((amenity) => (
                    <Badge key={amenity} variant="secondary" className="flex items-center gap-1">
                      {amenity}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeAmenity(amenity)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Media */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Media</h3>
              <FormField
                control={form.control}
                name="virtualTourUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Virtual Tour URL</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com/virtual-tour"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="space-y-2">
                <label className="text-sm font-medium">Images</label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add image URL"
                    value={newImage}
                    onChange={(e) => setNewImage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addImage())}
                    disabled={loading}
                  />
                  <Button type="button" onClick={addImage} disabled={loading} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {form.watch('images')?.map((image) => (
                    <Badge key={image} variant="outline" className="flex items-center gap-1 max-w-[200px]">
                      <span className="truncate">{image}</span>
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeImage(image)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Listing Details */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Listing Details</h3>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="mlsNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>MLS Number</FormLabel>
                      <FormControl>
                        <Input placeholder="MLS123456" {...field} disabled={loading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="daysOnMarket"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Days on Market</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="30"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : '')}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="listingDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Listing Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="expirationDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiration Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Status Options */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Status Options</h3>
              <FormField
                control={form.control}
                name="isFeatured"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={loading}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Featured Property</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Mark this property as featured for special promotion
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
                className="mt-2 sm:mt-0"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  isEditing ? 'Update Property' : 'Create Property'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
