'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Search, Filter } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import {
  PropertiesQueryParams,
  PropertyStatus,
  PropertyType,
} from '@/types/property';

// Advanced search form schema
const advancedSearchSchema = z.object({
  search: z.string().optional(),
  propertyType: z.nativeEnum(PropertyType).optional(),
  status: z.nativeEnum(PropertyStatus).optional(),

  // Location filters
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),

  // Size filters
  minSquareMeters: z.number().positive().optional(),
  maxSquareMeters: z.number().positive().optional(),
  minLotSize: z.number().positive().optional(),
  maxLotSize: z.number().positive().optional(),

  // Room filters
  minBedrooms: z.number().min(0).optional(),
  maxBedrooms: z.number().min(0).optional(),
  minBathrooms: z.number().min(0).optional(),
  maxBathrooms: z.number().min(0).optional(),

  // Price filters
  minPrice: z.number().positive().optional(),
  maxPrice: z.number().positive().optional(),
  priceType: z.enum(['listPrice', 'salePrice', 'rentPrice']).optional(),

  // Date filters
  listedAfter: z.string().optional(),
  listedBefore: z.string().optional(),

  // Status filters
  isFeatured: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

type AdvancedSearchValues = z.infer<typeof advancedSearchSchema>;

interface AdvancedSearchDialogProps {
  onSearch: (params: PropertiesQueryParams) => void;
  currentParams: PropertiesQueryParams;
}

export function AdvancedSearchDialog({ onSearch, currentParams }: AdvancedSearchDialogProps) {
  const [open, setOpen] = useState(false);

  const form = useForm<AdvancedSearchValues>({
    resolver: zodResolver(advancedSearchSchema),
    defaultValues: {
      search: currentParams.search || '',
      propertyType: currentParams.propertyType,
      status: currentParams.status,
      city: '',
      state: '',
      zipCode: '',
      minSquareMeters: undefined,
      maxSquareMeters: undefined,
      minLotSize: undefined,
      maxLotSize: undefined,
      minBedrooms: undefined,
      maxBedrooms: undefined,
      minBathrooms: undefined,
      maxBathrooms: undefined,
      minPrice: undefined,
      maxPrice: undefined,
      priceType: 'listPrice',
      listedAfter: '',
      listedBefore: '',
      isFeatured: undefined,
      isActive: undefined,
    },
  });

  const onSubmit = (data: AdvancedSearchValues) => {
    // Convert form data to query parameters
    const queryParams: PropertiesQueryParams = {
      ...currentParams,
      search: data.search || undefined,
      propertyType: data.propertyType,
      status: data.status,
      city: data.city || undefined,
      state: data.state || undefined,
      zipCode: data.zipCode || undefined,
      minSquareMeters: data.minSquareMeters,
      maxSquareMeters: data.maxSquareMeters,
      minLotSize: data.minLotSize,
      maxLotSize: data.maxLotSize,
      minBedrooms: data.minBedrooms,
      maxBedrooms: data.maxBedrooms,
      minBathrooms: data.minBathrooms,
      maxBathrooms: data.maxBathrooms,
      minPrice: data.minPrice,
      maxPrice: data.maxPrice,
      priceType: data.priceType,
      listedAfter: data.listedAfter || undefined,
      listedBefore: data.listedBefore || undefined,
      isFeatured: data.isFeatured,
      isActive: data.isActive,
      page: 1, // Reset to first page
    };

    onSearch(queryParams);
    setOpen(false);
  };

  const handleReset = () => {
    form.reset({
      search: '',
      propertyType: undefined,
      status: undefined,
      city: '',
      state: '',
      zipCode: '',
      minSquareMeters: undefined,
      maxSquareMeters: undefined,
      minLotSize: undefined,
      maxLotSize: undefined,
      minBedrooms: undefined,
      maxBedrooms: undefined,
      minBathrooms: undefined,
      maxBathrooms: undefined,
      minPrice: undefined,
      maxPrice: undefined,
      priceType: 'listPrice',
      listedAfter: '',
      listedBefore: '',
      isFeatured: undefined,
      isActive: undefined,
    });
  };

  const hasActiveFilters = () => {
    const values = form.getValues();
    return Object.entries(values).some(([key, value]) => {
      if (key === 'search') return false; // Search is handled separately
      if (key === 'priceType') return false; // Default value
      return value !== undefined && value !== '';
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Filter className="h-4 w-4 mr-2" />
          Advanced Search
          {hasActiveFilters() && (
            <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs">
              !
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Advanced Property Search</span>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Search */}
            <FormField
              control={form.control}
              name="search"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Search Text</FormLabel>
                  <FormControl>
                    <Input placeholder="Search by title, address, or MLS number..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Property Classification */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Property Classification</h3>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="propertyType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property Type</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Any type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Any Type</SelectItem>
                          {Object.values(PropertyType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Any status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Any Status</SelectItem>
                          {Object.values(PropertyStatus).map((status) => (
                            <SelectItem key={status} value={status}>
                              {status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Location */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Location</h3>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter city" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter state" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="zipCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ZIP Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter ZIP code" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset
              </Button>
              <Button type="button" variant="ghost" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Apply Filters
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
