'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Trash2,
  MapPin,
  DollarSign,
  Calendar,
  Building,
  Home,
  Ruler,
  User,
  Star,
  Eye,
  Link,
  Tag,
  FileText,
  Image,
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

import { Property, PropertyStatus, PropertyType } from '@/types/property';
import { usePropertyOperations } from '@/hooks/use-properties';

interface PropertyDetailViewProps {
  property: Property;
  onEdit: () => void;
  onDeleted: () => void;
}

export function PropertyDetailView({ property, onEdit, onDeleted }: PropertyDetailViewProps) {
  const router = useRouter();
  const { deleteProperty, loading } = usePropertyOperations();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteProperty(property.id);
      onDeleted();
      router.push('/dashboard/properties');
    } catch (error) {
      console.error('Delete error:', error);
    }
    setDeleteDialogOpen(false);
  };

  const getStatusBadgeVariant = (status: PropertyStatus) => {
    switch (status) {
      case PropertyStatus.AVAILABLE:
        return 'default';
      case PropertyStatus.UNDER_CONTRACT:
        return 'secondary';
      case PropertyStatus.SOLD:
        return 'default';
      case PropertyStatus.RENTED:
        return 'secondary';
      case PropertyStatus.OFF_MARKET:
        return 'outline';
      case PropertyStatus.COMING_SOON:
        return 'secondary';
      case PropertyStatus.WITHDRAWN:
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getTypeBadgeVariant = (type: PropertyType) => {
    switch (type) {
      case PropertyType.SINGLE_FAMILY:
      case PropertyType.CONDO:
      case PropertyType.TOWNHOUSE:
        return 'default';
      case PropertyType.MULTI_FAMILY:
      case PropertyType.RENTAL:
        return 'secondary';
      case PropertyType.COMMERCIAL:
      case PropertyType.INDUSTRIAL:
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatArea = (area: number) => {
    return `${area.toLocaleString()} m²`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{property.title}</h1>
            <p className="text-muted-foreground">Property ID: {property.id.slice(-8)}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={onEdit} className="flex items-center space-x-2">
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </Button>
          <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" className="flex items-center space-x-2">
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the property
                  &quot;{property.title}&quot; and remove its data from the system.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  disabled={loading}
                >
                  {loading ? 'Deleting...' : 'Delete'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Status and Type Badges */}
      <div className="flex items-center space-x-2">
        <Badge variant={getStatusBadgeVariant(property.status)} className="flex items-center space-x-1">
          <Building className="h-3 w-3" />
          <span>{property.status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</span>
        </Badge>
        <Badge variant={getTypeBadgeVariant(property.propertyType)} className="flex items-center space-x-1">
          <Home className="h-3 w-3" />
          <span>{property.propertyType.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</span>
        </Badge>
        {property.isFeatured && (
          <Badge variant="outline" className="flex items-center space-x-1">
            <Star className="h-3 w-3" />
            <span>Featured</span>
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Location Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Location</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p className="text-sm">{property.address}</p>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>{property.city}</span>
              {property.city && property.state && <span>,</span>}
              <span>{property.state}</span>
              {property.zipCode && <span>{property.zipCode}</span>}
            </div>
            <p className="text-sm text-muted-foreground">Country: {property.country}</p>
            {(property.latitude && property.longitude) && (
              <p className="text-sm text-muted-foreground">
                Coordinates: {property.latitude}, {property.longitude}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Property Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Home className="h-5 w-5" />
              <span>Property Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {(property.bedrooms || property.bathrooms) && (
              <div className="flex items-center space-x-3">
                <Home className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Rooms</p>
                  <p className="text-sm text-muted-foreground">
                    {property.bedrooms || 0} bed, {property.bathrooms || 0} bath
                  </p>
                </div>
              </div>
            )}
            {property.squareMeters && (
              <div className="flex items-center space-x-3">
                <Ruler className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Floor Area</p>
                  <p className="text-sm text-muted-foreground">{formatArea(property.squareMeters)}</p>
                </div>
              </div>
            )}
            {property.lotSizeMeters && (
              <div className="flex items-center space-x-3">
                <Ruler className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Lot Size</p>
                  <p className="text-sm text-muted-foreground">{formatArea(property.lotSizeMeters)}</p>
                </div>
              </div>
            )}
            {property.yearBuilt && (
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Year Built</p>
                  <p className="text-sm text-muted-foreground">{property.yearBuilt}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pricing Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>Pricing</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {property.listPrice && (
              <div className="flex items-center space-x-3">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">List Price</p>
                  <p className="text-lg font-semibold">{formatCurrency(property.listPrice)}</p>
                </div>
              </div>
            )}
            {property.salePrice && (
              <div className="flex items-center space-x-3">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Sale Price</p>
                  <p className="text-lg font-semibold">{formatCurrency(property.salePrice)}</p>
                </div>
              </div>
            )}
            {property.rentPrice && (
              <div className="flex items-center space-x-3">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Rent Price (Monthly)</p>
                  <p className="text-lg font-semibold">{formatCurrency(property.rentPrice)}</p>
                </div>
              </div>
            )}
            {property.pricePerSquareMeter && (
              <div className="flex items-center space-x-3">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Price per m²</p>
                  <p className="text-sm text-muted-foreground">{formatCurrency(property.pricePerSquareMeter)}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Features & Amenities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span>Features & Amenities</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {property.features && property.features.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Features</p>
                <div className="flex flex-wrap gap-2">
                  {property.features.map((feature) => (
                    <Badge key={feature} variant="outline">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            {property.amenities && property.amenities.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Amenities</p>
                <div className="flex flex-wrap gap-2">
                  {property.amenities.map((amenity) => (
                    <Badge key={amenity} variant="secondary">
                      {amenity}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Assignment & Listing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Assignment & Listing</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <User className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Assigned Agent</p>
                <p className="text-sm text-muted-foreground">
                  {property.assignee 
                    ? `${property.assignee.firstName} ${property.assignee.lastName}` 
                    : 'Unassigned'
                  }
                </p>
              </div>
            </div>
            {property.mlsNumber && (
              <div className="flex items-center space-x-3">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">MLS Number</p>
                  <p className="text-sm text-muted-foreground">{property.mlsNumber}</p>
                </div>
              </div>
            )}
            {property.daysOnMarket && (
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Days on Market</p>
                  <p className="text-sm text-muted-foreground">{property.daysOnMarket} days</p>
                </div>
              </div>
            )}
            {property.listingDate && (
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Listed Date</p>
                  <p className="text-sm text-muted-foreground">{formatDate(property.listingDate)}</p>
                </div>
              </div>
            )}
            {property.expirationDate && (
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Expiration Date</p>
                  <p className="text-sm text-muted-foreground">{formatDate(property.expirationDate)}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {property.description && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Description</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">{property.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Media */}
      {(property.images.length > 0 || property.virtualTourUrl) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Image className="h-5 w-5" />
              <span>Media</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {property.virtualTourUrl && (
              <div>
                <p className="text-sm font-medium mb-2">Virtual Tour</p>
                <a 
                  href={property.virtualTourUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
                >
                  <Link className="h-4 w-4" />
                  <span>View Virtual Tour</span>
                  <Eye className="h-4 w-4" />
                </a>
              </div>
            )}
            {property.images.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Images ({property.images.length})</p>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  {property.images.slice(0, 8).map((image, index) => (
                    <div key={index} className="aspect-square bg-muted rounded-md overflow-hidden">
                      <img 
                        src={image} 
                        alt={`Property image ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  ))}
                </div>
                {property.images.length > 8 && (
                  <p className="text-sm text-muted-foreground mt-2">
                    +{property.images.length - 8} more images
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Timeline</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Created</p>
                <p className="text-sm text-muted-foreground">{formatDate(property.createdAt)}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Last Updated</p>
                <p className="text-sm text-muted-foreground">{formatDate(property.updatedAt)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
