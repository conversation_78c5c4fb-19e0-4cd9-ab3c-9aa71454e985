"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { MoreVertical } from "lucide-react"
import Swal from "sweetalert2"

export type Client = {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  status: string
  type: string
}

interface DataTableProps {
  data: Client[]
  onEdit?: (client: Client) => void
}

export function DataTable({ data, onEdit }: DataTableProps) {
  const [clients, setClients] = useState<Client[]>(data)
  const router = useRouter()

  const handleDelete = async (id: string) => {
    const token = localStorage.getItem("token")
    if (!token) return toast.error("Unauthorized")

    try {
      Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!"
      }).then(async (result) => {
        if (result.isConfirmed) {
          const res = await fetch(`http://localhost:3000/api/clients/${id}`, {
            method: "DELETE",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })
          const result = await res.json()
          if (!res.ok) {
            toast.error(`Failed to delete: ${result.message}`)
            return
          }
          toast.success("Client deleted")
          setClients(prev => prev.filter(client => client.id !== id))
          Swal.fire({
            title: "Deleted!",
            text: "Your client has been deleted.",
            icon: "success"
          });
        }
      });




    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error"
      toast.error(`Error deleting client: ${errorMessage}`)
    }
  }

  return (
    <div className="overflow-auto rounded-md border shadow-sm">
      <Table>
        <TableHeader className="bg-muted">
          <TableRow>
            <TableHead>First Name</TableHead>
            <TableHead>Last Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Type</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {clients.length > 0 ? (
            clients.map(client => (
              <TableRow key={client.id}>
                <TableCell>{client.firstName}</TableCell>
                <TableCell>{client.lastName}</TableCell>
                <TableCell>{client.email}</TableCell>
                <TableCell>{client.phone}</TableCell>
                <TableCell>
                  <span className="rounded-full px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-800">
                    {client.status}
                  </span>
                </TableCell>
                <TableCell>{client.type}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => router.push(`/dashboard/clients/${client.id}`)}>
                        👁️ View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEdit?.(client)}>
                        ✏️ Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(client.id)}
                        className="text-red-600 focus:text-red-600"
                      >
                        🗑️ Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-8">
                No clients found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
