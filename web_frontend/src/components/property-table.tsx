'use client'

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { MoreHorizontal } from 'lucide-react'
import { toast } from 'sonner'
import Swal from 'sweetalert2'
import { useState } from 'react'
import { Property } from '@/types/property'

interface PropertyTableProps {
  data: Property[]
  onEdit?: (property: Property) => void
}

export function PropertyTable({ data, onEdit }: PropertyTableProps) {
  const router = useRouter()
  const [properties, setProperties] = useState(data)

  const handleDelete = async (id: string) => {
    const confirm = await Swal.fire({
      title: 'Are you sure?',
      text: 'You won\'t be able to revert this!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
    })

    if (!confirm.isConfirmed) return

    const token = localStorage.getItem('token')
    if (!token) return toast.error('Unauthorized')

    try {
      const res = await fetch(`http://localhost:3000/api/properties/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!res.ok) throw new Error('Failed to delete')

      // Update the list without reloading
      setProperties(prev => prev.filter(property => property.id !== id))

      toast.success('Property deleted successfully')
    } catch {
      toast.error('Error occurred while deleting')
    }
  }

  return (
    <div className="overflow-auto rounded-md border shadow-sm text-center">
      <Table>
        <TableHeader className="bg-muted">
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>City</TableHead>
            <TableHead>Country</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Agent</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {properties.length > 0 ? (
            properties.map(property => (
              <TableRow key={property.id}>
                <TableCell>{property.title}</TableCell>
                <TableCell>{property.propertyType}</TableCell>
                <TableCell>{property.status}</TableCell>
                <TableCell>${property.listPrice?.toLocaleString() || 'N/A'}</TableCell>
                <TableCell>{property.city}</TableCell>
                <TableCell>{property.country}</TableCell>
                <TableCell>{property.description}</TableCell>
                <TableCell>{property.address.substring(0, 20)}....</TableCell>
                <TableCell>
                  {property.assignee
                    ? `${property.assignee.firstName} ${property.assignee.lastName}`
                    : '—'}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => router.push(`/dashboard/properties/${property.id}/view`)}>
                        👁️ View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEdit?.(property)}>
                        ✏️ Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(property.id)}
                        className="text-red-600 focus:text-red-600"
                      >
                        🗑️ Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={10} className="text-center py-8">
                No properties available.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
