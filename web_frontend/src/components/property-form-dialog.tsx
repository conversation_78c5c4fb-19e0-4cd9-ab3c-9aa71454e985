"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Building, Building2 } from "lucide-react"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { toast } from "sonner"
import { PropertyType, PropertyStatus } from "@/constants/property"

export interface Property {
  id?: string
  title: string
  description: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  latitude?: number
  longitude?: number
  propertyType: string
  status: string
  bedrooms?: number
  bathrooms?: number
  squareMeters?: number
  lotSizeMeters?: number
  yearBuilt?: number
  listPrice?: number
  rentPrice?: number
  salePrice?: number
  pricePerSquareMeter?: number
  isFeatured?: boolean
}

interface PropertyFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  property?: Property | null
  onSuccess: () => void
}

export function PropertyFormDialog({ open, onOpenChange, property, onSuccess }: PropertyFormDialogProps) {
  const [formData, setFormData] = useState<Property>({
    title: "",
    description: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    country: "EG",
    latitude: 0,
    longitude: 0,
    propertyType: "",
    status: "",
    bedrooms: 0,
    bathrooms: 0,
    squareMeters: 0,
    lotSizeMeters: 0,
    yearBuilt: 0,
    listPrice: 0,
    rentPrice: 0,
    salePrice: 0,
    pricePerSquareMeter: 0,
    isFeatured: false,
  })
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const isEditing = !!property

  useEffect(() => {
    if (property) {
      setFormData(property)
    } else {
      setFormData({
        title: "",
        description: "",
        address: "",
        city: "",
        state: "",
        zipCode: "",
        country: "EG",
        latitude: 0,
        longitude: 0,
        propertyType: "",
        status: "",
        bedrooms: 0,
        bathrooms: 0,
        squareMeters: 0,
        lotSizeMeters: 0,
        yearBuilt: 0,
        listPrice: 0,
        rentPrice: 0,
        salePrice: 0,
        pricePerSquareMeter: 0,
        isFeatured: false,
      })
    }
    setError("")
  }, [property, open])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    // Basic validation
    if (!formData.title.trim() || !formData.address.trim() || !formData.city.trim() || !formData.state.trim()) {
      setError("Title, address, city, and state are required")
      setIsLoading(false)
      return
    }

    if (!formData.propertyType || !formData.status) {
      setError("Property type and status are required")
      setIsLoading(false)
      return
    }

    try {
      const token = localStorage.getItem("token")
      if (!token) throw new Error("Access token not found")

      const payload = {
        ...formData,
        features: [],
        amenities: [],
        images: [],
      }

      const url = isEditing
        ? `http://localhost:3000/api/properties/${property?.id}`
        : "http://localhost:3000/api/properties"

      const method = isEditing ? "PUT" : "POST"

      const res = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      })

      if (!res.ok) {
        const errorBody = await res.json()
        throw new Error(errorBody.message || `Failed to ${isEditing ? 'update' : 'add'} property`)
      }

      toast.success(`Property ${isEditing ? 'updated' : 'added'} successfully`)
      onSuccess()
      onOpenChange(false)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Something went wrong"
      console.error("Error:", errorMessage)
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <div className="flex items-center gap-2">
            {isEditing ? (
              <Building className="h-5 w-5 text-muted-foreground" />
            ) : (
              <Building2 className="h-5 w-5 text-muted-foreground" />
            )}
            <DialogTitle className="text-lg font-semibold">
              {isEditing ? "Edit Property" : "Add New Property"}
            </DialogTitle>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Property Title</Label>
              <Input
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Location Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                name="address"
                value={formData.address}
                onChange={handleChange}
                required
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="city">City</Label>
              <Input
                name="city"
                value={formData.city}
                onChange={handleChange}
                required
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="state">State</Label>
              <Input
                name="state"
                value={formData.state}
                onChange={handleChange}
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="zipCode">Zip Code</Label>
              <Input
                name="zipCode"
                value={formData.zipCode}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="country">Country</Label>
              <Input
                name="country"
                value={formData.country}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="latitude">Latitude</Label>
              <Input
                name="latitude"
                type="number"
                step="any"
                value={formData.latitude || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="longitude">Longitude</Label>
              <Input
                name="longitude"
                type="number"
                step="any"
                value={formData.longitude || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Property Type & Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="propertyType">Property Type</Label>
              <Select
                value={formData.propertyType}
                onValueChange={(value) => handleSelectChange('propertyType', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Type" />
                </SelectTrigger>
                <SelectContent>
                  {PropertyType.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  {PropertyStatus.map((status) => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Property Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="bedrooms">Bedrooms</Label>
              <Input
                name="bedrooms"
                type="number"
                value={formData.bedrooms || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="bathrooms">Bathrooms</Label>
              <Input
                name="bathrooms"
                type="number"
                value={formData.bathrooms || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="squareMeters">Square Meters</Label>
              <Input
                name="squareMeters"
                type="number"
                value={formData.squareMeters || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="lotSizeMeters">Lot Size (m²)</Label>
              <Input
                name="lotSizeMeters"
                type="number"
                value={formData.lotSizeMeters || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="yearBuilt">Year Built</Label>
              <Input
                name="yearBuilt"
                type="number"
                value={formData.yearBuilt || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="listPrice">List Price</Label>
              <Input
                name="listPrice"
                type="number"
                value={formData.listPrice || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="salePrice">Sale Price</Label>
              <Input
                name="salePrice"
                type="number"
                value={formData.salePrice || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="rentPrice">Rent Price</Label>
              <Input
                name="rentPrice"
                type="number"
                value={formData.rentPrice || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
            <div>
              <Label htmlFor="pricePerSquareMeter">Price per m²</Label>
              <Input
                name="pricePerSquareMeter"
                type="number"
                value={formData.pricePerSquareMeter || ''}
                onChange={handleChange}
                disabled={isLoading}
              />
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? "Updating..." : "Adding..."}
                </>
              ) : (
                isEditing ? "Update Property" : "Add Property"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
