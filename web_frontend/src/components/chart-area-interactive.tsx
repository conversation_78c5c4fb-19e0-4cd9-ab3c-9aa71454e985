"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"

import { useIsMobile } from "@/hooks/use-mobile"
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group"

export const description = "An interactive sales performance chart"

const chartData = [
  { date: "2024-04-01", leads: 45, deals: 12 },
  { date: "2024-04-02", leads: 38, deals: 8 },
  { date: "2024-04-03", leads: 52, deals: 15 },
  { date: "2024-04-04", leads: 61, deals: 18 },
  { date: "2024-04-05", leads: 73, deals: 22 },
  { date: "2024-04-06", leads: 58, deals: 16 },
  { date: "2024-04-07", leads: 42, deals: 11 },
  { date: "2024-04-08", leads: 67, deals: 19 },
  { date: "2024-04-09", leads: 35, deals: 9 },
  { date: "2024-04-10", leads: 49, deals: 14 },
  { date: "2024-04-11", leads: 68, deals: 20 },
  { date: "2024-04-12", leads: 54, deals: 13 },
  { date: "2024-04-13", leads: 71, deals: 21 },
  { date: "2024-04-14", leads: 41, deals: 10 },
  { date: "2024-04-15", leads: 38, deals: 12 },
  { date: "2024-04-16", leads: 43, deals: 11 },
  { date: "2024-04-17", leads: 76, deals: 23 },
  { date: "2024-04-18", leads: 69, deals: 18 },
  { date: "2024-04-19", leads: 47, deals: 14 },
  { date: "2024-04-20", leads: 33, deals: 8 },
  { date: "2024-04-21", leads: 45, deals: 13 },
  { date: "2024-04-22", leads: 51, deals: 15 },
  { date: "2024-04-23", leads: 44, deals: 12 },
  { date: "2024-04-24", leads: 72, deals: 19 },
  { date: "2024-04-25", leads: 56, deals: 16 },
  { date: "2024-04-26", leads: 31, deals: 7 },
  { date: "2024-04-27", leads: 78, deals: 24 },
  { date: "2024-04-28", leads: 39, deals: 11 },
  { date: "2024-04-29", leads: 64, deals: 17 },
  { date: "2024-04-30", leads: 81, deals: 25 },
  { date: "2024-05-01", leads: 48, deals: 14 },
  { date: "2024-05-02", leads: 66, deals: 18 },
  { date: "2024-05-03", leads: 53, deals: 15 },
  { date: "2024-05-04", leads: 77, deals: 22 },
  { date: "2024-05-05", leads: 84, deals: 26 },
  { date: "2024-05-06", leads: 89, deals: 28 },
  { date: "2024-05-07", leads: 71, deals: 20 },
  { date: "2024-05-08", leads: 46, deals: 13 },
  { date: "2024-05-09", leads: 52, deals: 16 },
  { date: "2024-05-10", leads: 68, deals: 19 },
  { date: "2024-05-11", leads: 74, deals: 21 },
  { date: "2024-05-12", leads: 58, deals: 17 },
  { date: "2024-05-13", leads: 44, deals: 12 },
  { date: "2024-05-14", leads: 86, deals: 27 },
  { date: "2024-05-15", leads: 79, deals: 24 },
  { date: "2024-05-16", leads: 69, deals: 20 },
  { date: "2024-05-17", leads: 92, deals: 29 },
  { date: "2024-05-18", leads: 67, deals: 19 },
  { date: "2024-05-19", leads: 55, deals: 16 },
  { date: "2024-05-20", leads: 49, deals: 14 },
  { date: "2024-05-21", leads: 36, deals: 9 },
  { date: "2024-05-22", leads: 34, deals: 8 },
  { date: "2024-05-23", leads: 63, deals: 18 },
  { date: "2024-05-24", leads: 70, deals: 21 },
  { date: "2024-05-25", leads: 57, deals: 16 },
  { date: "2024-05-26", leads: 48, deals: 13 },
  { date: "2024-05-27", leads: 85, deals: 26 },
  { date: "2024-05-28", leads: 54, deals: 15 },
  { date: "2024-05-29", leads: 32, deals: 8 },
  { date: "2024-05-30", leads: 75, deals: 22 },
  { date: "2024-05-31", leads: 51, deals: 14 },
  { date: "2024-06-01", leads: 53, deals: 15 },
  { date: "2024-06-02", leads: 88, deals: 27 },
  { date: "2024-06-03", leads: 41, deals: 11 },
  { date: "2024-06-04", leads: 82, deals: 25 },
  { date: "2024-06-05", leads: 37, deals: 9 },
  { date: "2024-06-06", leads: 65, deals: 18 },
  { date: "2024-06-07", leads: 73, deals: 21 },
  { date: "2024-06-08", leads: 78, deals: 23 },
  { date: "2024-06-09", leads: 91, deals: 28 },
  { date: "2024-06-10", leads: 46, deals: 13 },
  { date: "2024-06-11", leads: 39, deals: 10 },
  { date: "2024-06-12", leads: 95, deals: 30 },
  { date: "2024-06-13", leads: 35, deals: 8 },
  { date: "2024-06-14", leads: 83, deals: 25 },
  { date: "2024-06-15", leads: 72, deals: 20 },
  { date: "2024-06-16", leads: 76, deals: 22 },
  { date: "2024-06-17", leads: 98, deals: 31 },
  { date: "2024-06-18", leads: 43, deals: 12 },
  { date: "2024-06-19", leads: 71, deals: 19 },
  { date: "2024-06-20", leads: 87, deals: 26 },
  { date: "2024-06-21", leads: 48, deals: 14 },
  { date: "2024-06-22", leads: 69, deals: 18 },
  { date: "2024-06-23", leads: 102, deals: 32 },
  { date: "2024-06-24", leads: 44, deals: 11 },
  { date: "2024-06-25", leads: 47, deals: 13 },
  { date: "2024-06-26", leads: 84, deals: 24 },
  { date: "2024-06-27", leads: 89, deals: 27 },
  { date: "2024-06-28", leads: 52, deals: 15 },
  { date: "2024-06-29", leads: 41, deals: 10 },
  { date: "2024-06-30", leads: 93, deals: 29 },
]

const chartConfig = {
  sales: {
    label: "Sales Activity",
  },
  leads: {
    label: "New Leads",
    color: "var(--primary)",
  },
  deals: {
    label: "Closed Deals",
    color: "var(--primary)",
  },
} satisfies ChartConfig

export function ChartAreaInteractive() {
  const isMobile = useIsMobile()
  const [timeRange, setTimeRange] = React.useState("90d")

  React.useEffect(() => {
    if (isMobile) {
      setTimeRange("7d")
    }
  }, [isMobile])

  const filteredData = chartData.filter((item) => {
    const date = new Date(item.date)
    const referenceDate = new Date("2024-06-30")
    let daysToSubtract = 90
    if (timeRange === "30d") {
      daysToSubtract = 30
    } else if (timeRange === "7d") {
      daysToSubtract = 7
    }
    const startDate = new Date(referenceDate)
    startDate.setDate(startDate.getDate() - daysToSubtract)
    return date >= startDate
  })

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Sales Performance</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">
            Leads and deals closed over the last 3 months
          </span>
          <span className="@[540px]/card:hidden">Last 3 months performance</span>
        </CardDescription>
        <CardAction>
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
          >
            <ToggleGroupItem value="90d">Last 3 months</ToggleGroupItem>
            <ToggleGroupItem value="30d">Last 30 days</ToggleGroupItem>
            <ToggleGroupItem value="7d">Last 7 days</ToggleGroupItem>
          </ToggleGroup>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
              size="sm"
              aria-label="Select a value"
            >
              <SelectValue placeholder="Last 3 months" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="90d" className="rounded-lg">
                Last 3 months
              </SelectItem>
              <SelectItem value="30d" className="rounded-lg">
                Last 30 days
              </SelectItem>
              <SelectItem value="7d" className="rounded-lg">
                Last 7 days
              </SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillLeads" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-leads)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-leads)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillDeals" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-deals)"
                  stopOpacity={1.0}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-deals)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : 10}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    })
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="leads"
              type="natural"
              fill="url(#fillLeads)"
              stroke="var(--color-leads)"
              stackId="a"
            />
            <Area
              dataKey="deals"
              type="natural"
              fill="url(#fillDeals)"
              stroke="var(--color-deals)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
